<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()
const loading = ref(false)
const users = ref([])
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('all')
const sortBy = ref('created_at')
const sortOrder = ref('desc')

// 对话框状态
const showUserDetail = ref(false)
const showCreditDialog = ref(false)
const selectedUser = ref(null)

// 配额调整表单
const creditForm = ref({
  amount: 0,
  type: 'add', // add, subtract, set
  description: ''
})

// API 基础 URL
const apiUrl = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${adminStore.authToken}`
})

// 获取用户列表
const fetchUsers = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: pagination.value.limit.toString(),
      sortBy: sortBy.value,
      sortOrder: sortOrder.value
    })

    if (searchKeyword.value) {
      params.append('search', searchKeyword.value)
    }
    if (statusFilter.value !== 'all') {
      params.append('status', statusFilter.value)
    }

    const response = await fetch(`${apiUrl}/admin/users?${params}`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      users.value = result.users || []
      pagination.value = { ...pagination.value, ...result.pagination }
    } else {
      ElMessage.error(result.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查看用户详情
const handleViewUser = async (user: any) => {
  selectedUser.value = user
  showUserDetail.value = true
}

// 调整用户配额
const handleAdjustCredits = (user: any) => {
  selectedUser.value = user
  creditForm.value = {
    amount: 0,
    type: 'add',
    description: ''
  }
  showCreditDialog.value = true
}

// 提交配额调整
const submitCreditAdjustment = async () => {
  try {
    const response = await fetch(`${apiUrl}/admin/users/${selectedUser.value.id}/credits`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(creditForm.value)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('配额调整成功')
      showCreditDialog.value = false
      await fetchUsers(pagination.value.page)
    } else {
      ElMessage.error(result.message || '配额调整失败')
    }
  } catch (error) {
    console.error('配额调整失败:', error)
    ElMessage.error('配额调整失败')
  }
}

// 禁用/启用用户
const handleToggleUserStatus = async (user: any) => {
  const action = user.isActive ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 ${user.email} 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`${apiUrl}/admin/users/${user.id}/toggle-status`, {
      method: 'POST',
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(`用户${action}成功`)
      await fetchUsers(pagination.value.page)
    } else {
      ElMessage.error(result.message || `用户${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`用户${action}失败:`, error)
      ElMessage.error(`用户${action}失败`)
    }
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取状态颜色
const getStatusColor = (isActive: boolean) => {
  return isActive ? 'text-green-600' : 'text-red-600'
}

// 计算属性
const filteredUsers = computed(() => {
  return users.value.filter((user: any) => {
    const matchesSearch = !searchKeyword.value || 
      user.email.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      user.username?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const matchesStatus = statusFilter.value === 'all' || 
      (statusFilter.value === 'active' && user.isActive) ||
      (statusFilter.value === 'inactive' && !user.isActive)
    
    return matchesSearch && matchesStatus
  })
})

onMounted(() => {
  fetchUsers()
})
</script>

<template>
  <div class="users-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户管理</h1>
        <p class="page-subtitle">管理系统用户账户和配额</p>
      </div>
      
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-label">总用户数</span>
          <span class="stat-value">{{ pagination.total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">活跃用户</span>
          <span class="stat-value text-green-600">
            {{ filteredUsers.filter(u => u.isActive).length }}
          </span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索用户邮箱或用户名..."
          @input="fetchUsers(1)"
        />
      </div>
      
      <div class="filter-group">
        <select v-model="statusFilter" @change="fetchUsers(1)" class="filter-select">
          <option value="all">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">禁用</option>
        </select>
        
        <select v-model="sortBy" @change="fetchUsers(1)" class="filter-select">
          <option value="created_at">创建时间</option>
          <option value="last_login_at">最后登录</option>
          <option value="credits">配额余额</option>
        </select>
        
        <select v-model="sortOrder" @change="fetchUsers(1)" class="filter-select">
          <option value="desc">降序</option>
          <option value="asc">升序</option>
        </select>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-section">
      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
        <p class="mt-2 text-gray-600">加载中...</p>
      </div>

      <div v-else-if="filteredUsers.length === 0" class="empty-state">
        <i class="fas fa-users text-4xl text-gray-400"></i>
        <p class="mt-4 text-gray-600">暂无用户数据</p>
      </div>

      <div v-else class="users-table">
        <div class="table-header">
          <div class="col-user">用户信息</div>
          <div class="col-status">状态</div>
          <div class="col-credits">配额</div>
          <div class="col-emails">邮箱数</div>
          <div class="col-login">最后登录</div>
          <div class="col-created">注册时间</div>
          <div class="col-actions">操作</div>
        </div>

        <div class="table-body">
          <div 
            v-for="user in filteredUsers" 
            :key="user.id"
            class="table-row"
          >
            <div class="col-user">
              <div class="user-info">
                <div class="user-email">{{ user.email }}</div>
                <div class="user-meta">
                  ID: {{ user.id }}
                  <span v-if="user.username"> | {{ user.username }}</span>
                </div>
              </div>
            </div>
            <div class="col-status">
              <span :class="getStatusColor(user.isActive)">
                {{ user.isActive ? '活跃' : '禁用' }}
              </span>
            </div>
            <div class="col-credits">
              <div class="credits-info">
                <div class="credits-balance">{{ user.credits?.balance || 0 }}</div>
                <div class="credits-used">已用: {{ user.credits?.totalSpent || 0 }}</div>
              </div>
            </div>
            <div class="col-emails">{{ user.emailCount || 0 }}</div>
            <div class="col-login">
              {{ user.lastLoginAt ? formatDate(user.lastLoginAt) : '从未登录' }}
            </div>
            <div class="col-created">{{ formatDate(user.createdAt) }}</div>
            <div class="col-actions">
              <button @click="handleViewUser(user)" class="action-btn view-btn">
                <i class="fas fa-eye"></i>
              </button>
              <button @click="handleAdjustCredits(user)" class="action-btn credit-btn">
                <i class="fas fa-coins"></i>
              </button>
              <button 
                @click="handleToggleUserStatus(user)" 
                :class="[
                  'action-btn',
                  user.isActive ? 'disable-btn' : 'enable-btn'
                ]"
              >
                <i :class="user.isActive ? 'fas fa-ban' : 'fas fa-check'"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          v-for="page in pagination.totalPages" 
          :key="page"
          @click="fetchUsers(page)"
          :class="[
            'page-btn',
            { 'page-btn-active': page === pagination.page }
          ]"
        >
          {{ page }}
        </button>
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <div v-if="showUserDetail" class="modal-overlay" @click="showUserDetail = false">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <div>
            <h3>用户详情</h3>
            <p class="text-sm text-gray-600">{{ selectedUser?.email }}</p>
          </div>
          <button @click="showUserDetail = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="user-detail-grid">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-item">
                <label>用户ID</label>
                <span>{{ selectedUser?.id }}</span>
              </div>
              <div class="detail-item">
                <label>邮箱</label>
                <span>{{ selectedUser?.email }}</span>
              </div>
              <div class="detail-item">
                <label>用户名</label>
                <span>{{ selectedUser?.username || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>状态</label>
                <span :class="getStatusColor(selectedUser?.isActive)">
                  {{ selectedUser?.isActive ? '活跃' : '禁用' }}
                </span>
              </div>
            </div>

            <div class="detail-section">
              <h4>配额信息</h4>
              <div class="detail-item">
                <label>余额</label>
                <span>{{ selectedUser?.credits?.balance || 0 }}</span>
              </div>
              <div class="detail-item">
                <label>总获得</label>
                <span>{{ selectedUser?.credits?.totalEarned || 0 }}</span>
              </div>
              <div class="detail-item">
                <label>总消费</label>
                <span>{{ selectedUser?.credits?.totalSpent || 0 }}</span>
              </div>
            </div>

            <div class="detail-section">
              <h4>使用统计</h4>
              <div class="detail-item">
                <label>邮箱数量</label>
                <span>{{ selectedUser?.emailCount || 0 }}</span>
              </div>
              <div class="detail-item">
                <label>注册时间</label>
                <span>{{ formatDate(selectedUser?.createdAt) }}</span>
              </div>
              <div class="detail-item">
                <label>最后登录</label>
                <span>{{ selectedUser?.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : '从未登录' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配额调整对话框 -->
    <div v-if="showCreditDialog" class="modal-overlay" @click="showCreditDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>调整用户配额</h3>
          <button @click="showCreditDialog = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <form @submit.prevent="submitCreditAdjustment" class="modal-body">
          <div class="user-info-display">
            <p><strong>用户:</strong> {{ selectedUser?.email }}</p>
            <p><strong>当前余额:</strong> {{ selectedUser?.credits?.balance || 0 }}</p>
          </div>

          <div class="form-group">
            <label>操作类型</label>
            <select v-model="creditForm.type" class="form-control">
              <option value="add">增加配额</option>
              <option value="subtract">减少配额</option>
              <option value="set">设置配额</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>数量</label>
            <input 
              v-model.number="creditForm.amount" 
              type="number" 
              min="0" 
              class="form-control"
              required 
            />
          </div>
          
          <div class="form-group">
            <label>说明</label>
            <textarea 
              v-model="creditForm.description" 
              class="form-control"
              placeholder="调整原因说明..."
              rows="3"
            ></textarea>
          </div>
          
          <div class="modal-actions">
            <button type="button" @click="showCreditDialog = false" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary">
              确认调整
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.users-management {
  max-width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

/* 搜索和筛选 */
.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.filter-group {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

/* 用户列表 */
.users-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.users-table {
  width: 100%;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 2fr 0.8fr 1fr 0.8fr 1.2fr 1.2fr 1fr;
  gap: 16px;
  padding: 16px 24px;
  align-items: center;
}

.table-header {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background 0.2s ease;
}

.table-row:hover {
  background: #f9fafb;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-email {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.user-meta {
  font-size: 12px;
  color: #6b7280;
}

.credits-info {
  display: flex;
  flex-direction: column;
}

.credits-balance {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.credits-used {
  font-size: 12px;
  color: #6b7280;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 4px;
}

.view-btn {
  background: #dbeafe;
  color: #1e40af;
}

.view-btn:hover {
  background: #bfdbfe;
}

.credit-btn {
  background: #fef3c7;
  color: #d97706;
}

.credit-btn:hover {
  background: #fde68a;
}

.disable-btn {
  background: #fee2e2;
  color: #dc2626;
}

.disable-btn:hover {
  background: #fecaca;
}

.enable-btn {
  background: #dcfce7;
  color: #16a34a;
}

.enable-btn:hover {
  background: #bbf7d0;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 20px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background: #f3f4f6;
}

.page-btn-active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  margin: 20px;
}

.large-modal {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #374151;
}

.modal-body {
  padding: 24px;
}

/* 用户详情 */
.user-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.detail-section {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.detail-item span {
  font-weight: 500;
  color: #1f2937;
}

/* 表单 */
.user-info-display {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header,
  .table-row {
    grid-template-columns: 2fr 0.8fr 1fr 1fr 1fr;
  }
  
  .col-emails,
  .col-login {
    display: none;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-stats {
    justify-content: space-around;
  }
  
  .filters-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: stretch;
    flex-wrap: wrap;
  }
  
  .filter-select {
    flex: 1;
    min-width: 120px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    display: block;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
  }
  
  .user-detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>
