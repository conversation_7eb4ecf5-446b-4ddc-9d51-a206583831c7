<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()
const loading = ref(false)
const saving = ref(false)

// 系统设置
const settings = ref({
  // 邮箱设置
  emailSettings: {
    defaultDomains: ['ofun.my', 'ofun.io'],
    maxEmailsPerUser: 10,
    defaultEmailExpireDays: 30,
    enableAutoCleanup: true,
    cleanupDays: 7
  },
  
  // 用户设置
  userSettings: {
    enableRegistration: true,
    defaultCredits: 100,
    maxCreditsPerUser: 10000,
    enableEmailVerification: false
  },
  
  // 兑换码设置
  redemptionSettings: {
    enableRedemption: true,
    maxCodesPerBatch: 100,
    defaultCodeLength: 12
  },
  
  // 系统设置
  systemSettings: {
    siteName: 'TempMail',
    siteDescription: '临时邮箱服务',
    enableMaintenance: false,
    maintenanceMessage: '系统维护中，请稍后再试',
    enableAnalytics: true
  }
})

// API 基础 URL
const apiUrl = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${adminStore.authToken}`
})

// 获取系统设置
const fetchSettings = async () => {
  loading.value = true
  try {
    const response = await fetch(`${apiUrl}/admin/settings`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      settings.value = { ...settings.value, ...result.settings }
    } else {
      ElMessage.error(result.message || '获取系统设置失败')
    }
  } catch (error) {
    console.error('获取系统设置失败:', error)
    ElMessage.error('获取系统设置失败')
  } finally {
    loading.value = false
  }
}

// 保存系统设置
const saveSettings = async () => {
  saving.value = true
  try {
    const response = await fetch(`${apiUrl}/admin/settings`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(settings.value)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('系统设置保存成功')
    } else {
      ElMessage.error(result.message || '保存系统设置失败')
    }
  } catch (error) {
    console.error('保存系统设置失败:', error)
    ElMessage.error('保存系统设置失败')
  } finally {
    saving.value = false
  }
}

// 添加域名
const addDomain = () => {
  const domain = prompt('请输入新的域名:')
  if (domain && !settings.value.emailSettings.defaultDomains.includes(domain)) {
    settings.value.emailSettings.defaultDomains.push(domain)
  }
}

// 删除域名
const removeDomain = (index: number) => {
  if (settings.value.emailSettings.defaultDomains.length > 1) {
    settings.value.emailSettings.defaultDomains.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个域名')
  }
}

// 重置设置
const resetSettings = () => {
  if (confirm('确定要重置所有设置为默认值吗？')) {
    fetchSettings()
  }
}

onMounted(() => {
  fetchSettings()
})
</script>

<template>
  <div class="system-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">系统设置</h1>
        <p class="page-subtitle">配置系统参数和功能选项</p>
      </div>
      
      <div class="header-actions">
        <button @click="resetSettings" class="btn btn-secondary">
          <i class="fas fa-undo mr-2"></i>
          重置设置
        </button>
        <button @click="saveSettings" :disabled="saving" class="btn btn-primary">
          <i v-if="saving" class="fas fa-spinner fa-spin mr-2"></i>
          <i v-else class="fas fa-save mr-2"></i>
          {{ saving ? '保存中...' : '保存设置' }}
        </button>
      </div>
    </div>

    <!-- 设置内容 -->
    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
      <p class="mt-2 text-gray-600">加载中...</p>
    </div>

    <div v-else class="settings-content">
      <!-- 邮箱设置 -->
      <div class="settings-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-envelope mr-2"></i>
            邮箱设置
          </h2>
        </div>
        
        <div class="section-body">
          <div class="setting-item">
            <label class="setting-label">默认域名</label>
            <div class="domain-list">
              <div v-for="(domain, index) in settings.emailSettings.defaultDomains" :key="index" class="domain-item">
                <span>{{ domain }}</span>
                <button @click="removeDomain(index)" class="remove-btn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <button @click="addDomain" class="add-domain-btn">
                <i class="fas fa-plus mr-2"></i>
                添加域名
              </button>
            </div>
          </div>

          <div class="setting-row">
            <div class="setting-item">
              <label class="setting-label">每用户最大邮箱数</label>
              <input 
                v-model.number="settings.emailSettings.maxEmailsPerUser" 
                type="number" 
                min="1" 
                max="100"
                class="setting-input"
              />
            </div>
            
            <div class="setting-item">
              <label class="setting-label">默认过期天数</label>
              <input 
                v-model.number="settings.emailSettings.defaultEmailExpireDays" 
                type="number" 
                min="1" 
                max="365"
                class="setting-input"
              />
            </div>
          </div>

          <div class="setting-row">
            <div class="setting-item">
              <label class="setting-label">启用自动清理</label>
              <label class="switch">
                <input 
                  v-model="settings.emailSettings.enableAutoCleanup" 
                  type="checkbox"
                />
                <span class="slider"></span>
              </label>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">清理间隔（天）</label>
              <input 
                v-model.number="settings.emailSettings.cleanupDays" 
                type="number" 
                min="1" 
                max="30"
                class="setting-input"
                :disabled="!settings.emailSettings.enableAutoCleanup"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 用户设置 -->
      <div class="settings-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-users mr-2"></i>
            用户设置
          </h2>
        </div>
        
        <div class="section-body">
          <div class="setting-row">
            <div class="setting-item">
              <label class="setting-label">允许用户注册</label>
              <label class="switch">
                <input 
                  v-model="settings.userSettings.enableRegistration" 
                  type="checkbox"
                />
                <span class="slider"></span>
              </label>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">启用邮箱验证</label>
              <label class="switch">
                <input 
                  v-model="settings.userSettings.enableEmailVerification" 
                  type="checkbox"
                />
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <div class="setting-row">
            <div class="setting-item">
              <label class="setting-label">默认配额</label>
              <input 
                v-model.number="settings.userSettings.defaultCredits" 
                type="number" 
                min="0" 
                max="10000"
                class="setting-input"
              />
            </div>
            
            <div class="setting-item">
              <label class="setting-label">最大配额</label>
              <input 
                v-model.number="settings.userSettings.maxCreditsPerUser" 
                type="number" 
                min="100" 
                max="100000"
                class="setting-input"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 兑换码设置 -->
      <div class="settings-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-ticket-alt mr-2"></i>
            兑换码设置
          </h2>
        </div>
        
        <div class="section-body">
          <div class="setting-row">
            <div class="setting-item">
              <label class="setting-label">启用兑换码功能</label>
              <label class="switch">
                <input 
                  v-model="settings.redemptionSettings.enableRedemption" 
                  type="checkbox"
                />
                <span class="slider"></span>
              </label>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">默认兑换码长度</label>
              <input 
                v-model.number="settings.redemptionSettings.defaultCodeLength" 
                type="number" 
                min="6" 
                max="20"
                class="setting-input"
              />
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label">批量生成最大数量</label>
            <input 
              v-model.number="settings.redemptionSettings.maxCodesPerBatch" 
              type="number" 
              min="1" 
              max="1000"
              class="setting-input"
            />
          </div>
        </div>
      </div>

      <!-- 系统设置 -->
      <div class="settings-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-cog mr-2"></i>
            系统设置
          </h2>
        </div>
        
        <div class="section-body">
          <div class="setting-row">
            <div class="setting-item">
              <label class="setting-label">网站名称</label>
              <input 
                v-model="settings.systemSettings.siteName" 
                type="text"
                class="setting-input"
              />
            </div>
            
            <div class="setting-item">
              <label class="setting-label">网站描述</label>
              <input 
                v-model="settings.systemSettings.siteDescription" 
                type="text"
                class="setting-input"
              />
            </div>
          </div>

          <div class="setting-row">
            <div class="setting-item">
              <label class="setting-label">维护模式</label>
              <label class="switch">
                <input 
                  v-model="settings.systemSettings.enableMaintenance" 
                  type="checkbox"
                />
                <span class="slider"></span>
              </label>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">启用统计分析</label>
              <label class="switch">
                <input 
                  v-model="settings.systemSettings.enableAnalytics" 
                  type="checkbox"
                />
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label">维护提示信息</label>
            <textarea 
              v-model="settings.systemSettings.maintenanceMessage" 
              class="setting-textarea"
              rows="3"
              :disabled="!settings.systemSettings.enableMaintenance"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.system-settings {
  max-width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 设置内容 */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
}

.section-body {
  padding: 24px;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.setting-input,
.setting-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.setting-input:focus,
.setting-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-input:disabled,
.setting-textarea:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.setting-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.setting-row:last-child {
  margin-bottom: 0;
}

/* 域名列表 */
.domain-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.domain-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  font-size: 14px;
}

.remove-btn {
  padding: 2px 4px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  transition: background 0.2s ease;
}

.remove-btn:hover {
  background: #dc2626;
}

.add-domain-btn {
  padding: 6px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
}

.add-domain-btn:hover {
  background: #2563eb;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #3b82f6;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .setting-row {
    grid-template-columns: 1fr;
  }
  
  .domain-list {
    flex-direction: column;
    align-items: stretch;
  }
  
  .domain-item {
    justify-content: space-between;
  }
}
</style>
