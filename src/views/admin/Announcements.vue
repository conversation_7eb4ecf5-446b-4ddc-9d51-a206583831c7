<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()
const loading = ref(false)
const announcements = ref([])
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('all')
const typeFilter = ref('all')

// 对话框状态
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const selectedAnnouncement = ref(null)

// 表单数据
const announcementForm = ref({
  title: '',
  content: '',
  type: 'info',
  priority: 'normal',
  isActive: true,
  expiresAt: ''
})

// API 基础 URL
const apiUrl = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${adminStore.authToken}`
})

// 获取公告列表
const fetchAnnouncements = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: pagination.value.limit.toString()
    })

    if (searchKeyword.value) {
      params.append('search', searchKeyword.value)
    }
    if (statusFilter.value !== 'all') {
      params.append('status', statusFilter.value)
    }
    if (typeFilter.value !== 'all') {
      params.append('type', typeFilter.value)
    }

    const response = await fetch(`${apiUrl}/admin/announcements?${params}`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      announcements.value = result.announcements || []
      pagination.value = { ...pagination.value, ...result.pagination }
    } else {
      ElMessage.error(result.message || '获取公告列表失败')
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    loading.value = false
  }
}

// 创建公告
const handleCreateAnnouncement = async () => {
  try {
    const response = await fetch(`${apiUrl}/admin/announcements`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(announcementForm.value)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('公告创建成功')
      showCreateDialog.value = false
      resetForm()
      await fetchAnnouncements()
    } else {
      ElMessage.error(result.message || '创建公告失败')
    }
  } catch (error) {
    console.error('创建公告失败:', error)
    ElMessage.error('创建公告失败')
  }
}

// 编辑公告
const handleEditAnnouncement = (announcement: any) => {
  selectedAnnouncement.value = announcement
  announcementForm.value = {
    title: announcement.title,
    content: announcement.content,
    type: announcement.type,
    priority: announcement.priority,
    isActive: announcement.isActive,
    expiresAt: announcement.expiresAt || ''
  }
  showEditDialog.value = true
}

// 更新公告
const handleUpdateAnnouncement = async () => {
  try {
    const response = await fetch(`${apiUrl}/admin/announcements/${selectedAnnouncement.value.id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(announcementForm.value)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('公告更新成功')
      showEditDialog.value = false
      resetForm()
      await fetchAnnouncements()
    } else {
      ElMessage.error(result.message || '更新公告失败')
    }
  } catch (error) {
    console.error('更新公告失败:', error)
    ElMessage.error('更新公告失败')
  }
}

// 删除公告
const handleDeleteAnnouncement = async (announcement: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除公告 "${announcement.title}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`${apiUrl}/admin/announcements/${announcement.id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('公告删除成功')
      await fetchAnnouncements()
    } else {
      ElMessage.error(result.message || '删除公告失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除公告失败:', error)
      ElMessage.error('删除公告失败')
    }
  }
}

// 切换公告状态
const handleToggleStatus = async (announcement: any) => {
  try {
    const response = await fetch(`${apiUrl}/admin/announcements/${announcement.id}/toggle`, {
      method: 'POST',
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(`公告已${announcement.isActive ? '禁用' : '启用'}`)
      await fetchAnnouncements()
    } else {
      ElMessage.error(result.message || '状态切换失败')
    }
  } catch (error) {
    console.error('状态切换失败:', error)
    ElMessage.error('状态切换失败')
  }
}

// 重置表单
const resetForm = () => {
  announcementForm.value = {
    title: '',
    content: '',
    type: 'info',
    priority: 'normal',
    isActive: true,
    expiresAt: ''
  }
  selectedAnnouncement.value = null
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  switch (type) {
    case 'info': return 'text-blue-600'
    case 'warning': return 'text-yellow-600'
    case 'error': return 'text-red-600'
    case 'success': return 'text-green-600'
    default: return 'text-gray-600'
  }
}

// 获取类型文本
const getTypeText = (type: string) => {
  switch (type) {
    case 'info': return '信息'
    case 'warning': return '警告'
    case 'error': return '错误'
    case 'success': return '成功'
    default: return '未知'
  }
}

// 获取优先级颜色
const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high': return 'text-red-600'
    case 'normal': return 'text-blue-600'
    case 'low': return 'text-gray-600'
    default: return 'text-gray-600'
  }
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'high': return '高'
    case 'normal': return '普通'
    case 'low': return '低'
    default: return '未知'
  }
}

// 计算属性
const filteredAnnouncements = computed(() => {
  return announcements.value.filter((announcement: any) => {
    const matchesSearch = !searchKeyword.value || 
      announcement.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      announcement.content.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const matchesStatus = statusFilter.value === 'all' || 
      (statusFilter.value === 'active' && announcement.isActive) ||
      (statusFilter.value === 'inactive' && !announcement.isActive)
    
    const matchesType = typeFilter.value === 'all' || announcement.type === typeFilter.value
    
    return matchesSearch && matchesStatus && matchesType
  })
})

onMounted(() => {
  fetchAnnouncements()
})
</script>

<template>
  <div class="announcements-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">系统公告</h1>
        <p class="page-subtitle">管理系统公告和通知信息</p>
      </div>
      
      <div class="header-actions">
        <button @click="showCreateDialog = true" class="btn btn-primary">
          <i class="fas fa-plus mr-2"></i>
          创建公告
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索公告标题或内容..."
          @input="fetchAnnouncements(1)"
        />
      </div>
      
      <div class="filter-group">
        <select v-model="statusFilter" @change="fetchAnnouncements(1)" class="filter-select">
          <option value="all">全部状态</option>
          <option value="active">启用</option>
          <option value="inactive">禁用</option>
        </select>
        
        <select v-model="typeFilter" @change="fetchAnnouncements(1)" class="filter-select">
          <option value="all">全部类型</option>
          <option value="info">信息</option>
          <option value="warning">警告</option>
          <option value="error">错误</option>
          <option value="success">成功</option>
        </select>
      </div>
    </div>

    <!-- 公告列表 -->
    <div class="announcements-section">
      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
        <p class="mt-2 text-gray-600">加载中...</p>
      </div>

      <div v-else-if="filteredAnnouncements.length === 0" class="empty-state">
        <i class="fas fa-bullhorn text-4xl text-gray-400"></i>
        <p class="mt-4 text-gray-600">暂无公告数据</p>
      </div>

      <div v-else class="announcements-table">
        <div class="table-header">
          <div class="col-title">标题</div>
          <div class="col-type">类型</div>
          <div class="col-priority">优先级</div>
          <div class="col-status">状态</div>
          <div class="col-created">创建时间</div>
          <div class="col-expires">过期时间</div>
          <div class="col-actions">操作</div>
        </div>

        <div class="table-body">
          <div 
            v-for="announcement in filteredAnnouncements" 
            :key="announcement.id"
            class="table-row"
          >
            <div class="col-title">
              <div class="announcement-info">
                <div class="announcement-title">{{ announcement.title }}</div>
                <div class="announcement-content">{{ announcement.content.substring(0, 100) }}...</div>
              </div>
            </div>
            <div class="col-type">
              <span :class="getTypeColor(announcement.type)">
                {{ getTypeText(announcement.type) }}
              </span>
            </div>
            <div class="col-priority">
              <span :class="getPriorityColor(announcement.priority)">
                {{ getPriorityText(announcement.priority) }}
              </span>
            </div>
            <div class="col-status">
              <span :class="announcement.isActive ? 'text-green-600' : 'text-red-600'">
                {{ announcement.isActive ? '启用' : '禁用' }}
              </span>
            </div>
            <div class="col-created">{{ formatDate(announcement.createdAt) }}</div>
            <div class="col-expires">
              {{ announcement.expiresAt ? formatDate(announcement.expiresAt) : '永不过期' }}
            </div>
            <div class="col-actions">
              <button @click="handleEditAnnouncement(announcement)" class="action-btn edit-btn">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="handleToggleStatus(announcement)" class="action-btn toggle-btn">
                <i :class="announcement.isActive ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
              <button @click="handleDeleteAnnouncement(announcement)" class="action-btn delete-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          v-for="page in pagination.totalPages" 
          :key="page"
          @click="fetchAnnouncements(page)"
          :class="[
            'page-btn',
            { 'page-btn-active': page === pagination.page }
          ]"
        >
          {{ page }}
        </button>
      </div>
    </div>

    <!-- 创建公告对话框 -->
    <div v-if="showCreateDialog" class="modal-overlay" @click="showCreateDialog = false">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>创建系统公告</h3>
          <button @click="showCreateDialog = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <form @submit.prevent="handleCreateAnnouncement" class="modal-body">
          <div class="form-group">
            <label>公告标题</label>
            <input v-model="announcementForm.title" type="text" placeholder="输入公告标题" required />
          </div>
          
          <div class="form-group">
            <label>公告内容</label>
            <textarea 
              v-model="announcementForm.content" 
              placeholder="输入公告内容" 
              rows="6"
              required
            ></textarea>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>类型</label>
              <select v-model="announcementForm.type">
                <option value="info">信息</option>
                <option value="warning">警告</option>
                <option value="error">错误</option>
                <option value="success">成功</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>优先级</label>
              <select v-model="announcementForm.priority">
                <option value="low">低</option>
                <option value="normal">普通</option>
                <option value="high">高</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>状态</label>
              <select v-model="announcementForm.isActive">
                <option :value="true">启用</option>
                <option :value="false">禁用</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>过期时间（可选）</label>
              <input v-model="announcementForm.expiresAt" type="datetime-local" />
            </div>
          </div>
          
          <div class="modal-actions">
            <button type="button" @click="showCreateDialog = false" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary">
              创建
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 编辑公告对话框 -->
    <div v-if="showEditDialog" class="modal-overlay" @click="showEditDialog = false">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>编辑系统公告</h3>
          <button @click="showEditDialog = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <form @submit.prevent="handleUpdateAnnouncement" class="modal-body">
          <div class="form-group">
            <label>公告标题</label>
            <input v-model="announcementForm.title" type="text" placeholder="输入公告标题" required />
          </div>
          
          <div class="form-group">
            <label>公告内容</label>
            <textarea 
              v-model="announcementForm.content" 
              placeholder="输入公告内容" 
              rows="6"
              required
            ></textarea>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>类型</label>
              <select v-model="announcementForm.type">
                <option value="info">信息</option>
                <option value="warning">警告</option>
                <option value="error">错误</option>
                <option value="success">成功</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>优先级</label>
              <select v-model="announcementForm.priority">
                <option value="low">低</option>
                <option value="normal">普通</option>
                <option value="high">高</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>状态</label>
              <select v-model="announcementForm.isActive">
                <option :value="true">启用</option>
                <option :value="false">禁用</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>过期时间（可选）</label>
              <input v-model="announcementForm.expiresAt" type="datetime-local" />
            </div>
          </div>
          
          <div class="modal-actions">
            <button type="button" @click="showEditDialog = false" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary">
              更新
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.announcements-management {
  max-width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* 搜索和筛选 */
.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.filter-group {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

/* 公告列表 */
.announcements-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.announcements-table {
  width: 100%;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 3fr 0.8fr 0.8fr 0.8fr 1.2fr 1.2fr 1fr;
  gap: 16px;
  padding: 16px 24px;
  align-items: center;
}

.table-header {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background 0.2s ease;
}

.table-row:hover {
  background: #f9fafb;
}

.announcement-info {
  display: flex;
  flex-direction: column;
}

.announcement-title {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.announcement-content {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 4px;
}

.edit-btn {
  background: #dbeafe;
  color: #1e40af;
}

.edit-btn:hover {
  background: #bfdbfe;
}

.toggle-btn {
  background: #fef3c7;
  color: #d97706;
}

.toggle-btn:hover {
  background: #fde68a;
}

.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover {
  background: #fecaca;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 20px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background: #f3f4f6;
}

.page-btn-active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  margin: 20px;
}

.large-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header,
  .table-row {
    grid-template-columns: 3fr 0.8fr 0.8fr 1fr 1fr;
  }
  
  .col-priority,
  .col-expires {
    display: none;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filters-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: stretch;
  }
  
  .filter-select {
    flex: 1;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    display: block;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style>
