<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const router = useRouter()
const route = useRoute()
const adminStore = useAdminStore()

const sidebarCollapsed = ref(false)

// 菜单项配置
const menuItems = [
  {
    path: '/admin/dashboard',
    name: 'dashboard',
    title: '仪表盘',
    icon: 'fas fa-tachometer-alt'
  },
  {
    path: '/admin/specified-emails',
    name: 'specified-emails',
    title: '指定邮箱管理',
    icon: 'fas fa-envelope-open-text'
  },
  {
    path: '/admin/user-temp-emails',
    name: 'user-temp-emails',
    title: '用户临时邮箱管理',
    icon: 'fas fa-users'
  },
  {
    path: '/admin/users',
    name: 'users',
    title: '用户管理',
    icon: 'fas fa-user-friends'
  },
  {
    path: '/admin/redemption-codes',
    name: 'redemption-codes',
    title: '兑换码管理',
    icon: 'fas fa-ticket-alt'
  },
  {
    path: '/admin/announcements',
    name: 'announcements',
    title: '系统公告',
    icon: 'fas fa-bullhorn'
  },
  {
    path: '/admin/settings',
    name: 'settings',
    title: '系统设置',
    icon: 'fas fa-cog'
  }
]

// 当前激活的菜单项
const activeMenu = computed(() => {
  const currentPath = route.path
  return menuItems.find(item => currentPath.startsWith(item.path))?.name || 'dashboard'
})

// 页面标题
const pageTitle = computed(() => {
  return menuItems.find(item => item.name === activeMenu.value)?.title || '管理后台'
})

// 处理菜单点击
const handleMenuClick = (path: string) => {
  router.push(path)
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    adminStore.logout()
    ElMessage.success('已退出登录')
    router.push('/admin/login')
  } catch {
    // 用户取消
  }
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

onMounted(() => {
  // 检查登录状态
  if (!adminStore.isLoggedIn) {
    router.push('/admin/login')
  }
})
</script>

<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <aside 
      :class="[
        'sidebar',
        { 'sidebar-collapsed': sidebarCollapsed }
      ]"
    >
      <!-- Logo 区域 -->
      <div class="sidebar-header">
        <div class="logo">
          <i class="fas fa-shield-alt"></i>
          <span v-show="!sidebarCollapsed" class="logo-text">管理后台</span>
        </div>
      </div>

      <!-- 菜单列表 -->
      <nav class="sidebar-nav">
        <ul class="nav-list">
          <li 
            v-for="item in menuItems" 
            :key="item.name"
            :class="[
              'nav-item',
              { 'nav-item-active': activeMenu === item.name }
            ]"
            @click="handleMenuClick(item.path)"
          >
            <div class="nav-link">
              <i :class="item.icon"></i>
              <span v-show="!sidebarCollapsed" class="nav-text">{{ item.title }}</span>
            </div>
          </li>
        </ul>
      </nav>

      <!-- 侧边栏底部 -->
      <div class="sidebar-footer">
        <button 
          @click="toggleSidebar"
          class="toggle-btn"
          :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
        >
          <i :class="sidebarCollapsed ? 'fas fa-angle-right' : 'fas fa-angle-left'"></i>
        </button>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <h1 class="page-title">{{ pageTitle }}</h1>
        </div>
        
        <div class="header-right">
          <div class="admin-info">
            <i class="fas fa-user-shield"></i>
            <span>管理员</span>
          </div>
          
          <button @click="handleLogout" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            <span>退出登录</span>
          </button>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="page-content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<style scoped>
.admin-layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
}

/* 侧边栏样式 */
.sidebar {
  width: 260px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.logo i {
  font-size: 24px;
  color: #3498db;
  margin-right: 12px;
}

.logo-text {
  transition: opacity 0.3s ease;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin: 4px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.nav-item-active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  text-decoration: none;
  color: inherit;
}

.nav-link i {
  font-size: 16px;
  width: 20px;
  margin-right: 12px;
  text-align: center;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  transition: opacity 0.3s ease;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.toggle-btn {
  width: 100%;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: background 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.top-header {
  height: 70px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.logout-btn:hover {
  background: #dc2626;
}

.page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    height: 100vh;
  }
  
  .sidebar-collapsed {
    transform: translateX(-100%);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .logout-btn span {
    display: none;
  }
}
</style>
