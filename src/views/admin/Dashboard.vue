<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()
const loading = ref(false)

// 统计数据
const stats = ref({
  totalUsers: 0,
  activeUsers: 0,
  totalEmails: 0,
  userTempEmails: 0,
  specifiedEmails: 0,
  totalMessages: 0,
  todayMessages: 0,
  totalCredits: 0,
  usedCredits: 0,
  activeCodes: 0,
  announcements: 0
})

// 最近活动
const recentActivities = ref([])

// API 基础 URL
const apiUrl = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${adminStore.authToken}`
})

// 获取仪表盘数据
const fetchDashboardData = async () => {
  loading.value = true
  try {
    // 这里应该调用仪表盘统计 API
    // 暂时使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    stats.value = {
      totalUsers: 1250,
      activeUsers: 890,
      totalEmails: 3420,
      userTempEmails: 2100,
      specifiedEmails: 1320,
      totalMessages: 15680,
      todayMessages: 234,
      totalCredits: 45000,
      usedCredits: 12500,
      activeCodes: 25,
      announcements: 8
    }

    recentActivities.value = [
      {
        id: 1,
        type: 'user_register',
        description: '新用户注册',
        user: '<EMAIL>',
        time: '2分钟前'
      },
      {
        id: 2,
        type: 'email_created',
        description: '创建临时邮箱',
        user: '<EMAIL>',
        time: '5分钟前'
      },
      {
        id: 3,
        type: 'code_redeemed',
        description: '兑换码使用',
        user: '<EMAIL>',
        time: '10分钟前'
      }
    ]
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 计算属性
const userGrowthRate = computed(() => {
  return ((stats.value.activeUsers / stats.value.totalUsers) * 100).toFixed(1)
})

const creditUsageRate = computed(() => {
  return ((stats.value.usedCredits / stats.value.totalCredits) * 100).toFixed(1)
})

// 获取活动类型图标
const getActivityIcon = (type: string) => {
  switch (type) {
    case 'user_register': return 'fas fa-user-plus text-green-500'
    case 'email_created': return 'fas fa-envelope text-blue-500'
    case 'code_redeemed': return 'fas fa-ticket-alt text-purple-500'
    default: return 'fas fa-info-circle text-gray-500'
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<template>
  <div class="dashboard">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin text-4xl text-blue-500"></i>
        <p class="mt-4 text-gray-600">加载中...</p>
      </div>
    </div>

    <!-- 仪表盘内容 -->
    <div v-else class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <!-- 用户统计 -->
        <div class="stat-card">
          <div class="stat-icon bg-blue-500">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-content">
            <h3 class="stat-title">用户统计</h3>
            <div class="stat-number">{{ stats.totalUsers.toLocaleString() }}</div>
            <div class="stat-subtitle">
              活跃用户: {{ stats.activeUsers.toLocaleString() }} ({{ userGrowthRate }}%)
            </div>
          </div>
        </div>

        <!-- 邮箱统计 -->
        <div class="stat-card">
          <div class="stat-icon bg-green-500">
            <i class="fas fa-envelope"></i>
          </div>
          <div class="stat-content">
            <h3 class="stat-title">邮箱统计</h3>
            <div class="stat-number">{{ stats.totalEmails.toLocaleString() }}</div>
            <div class="stat-subtitle">
              用户邮箱: {{ stats.userTempEmails.toLocaleString() }} | 
              指定邮箱: {{ stats.specifiedEmails.toLocaleString() }}
            </div>
          </div>
        </div>

        <!-- 邮件统计 -->
        <div class="stat-card">
          <div class="stat-icon bg-purple-500">
            <i class="fas fa-paper-plane"></i>
          </div>
          <div class="stat-content">
            <h3 class="stat-title">邮件统计</h3>
            <div class="stat-number">{{ stats.totalMessages.toLocaleString() }}</div>
            <div class="stat-subtitle">
              今日新增: {{ stats.todayMessages.toLocaleString() }}
            </div>
          </div>
        </div>

        <!-- 配额统计 -->
        <div class="stat-card">
          <div class="stat-icon bg-yellow-500">
            <i class="fas fa-coins"></i>
          </div>
          <div class="stat-content">
            <h3 class="stat-title">配额统计</h3>
            <div class="stat-number">{{ stats.totalCredits.toLocaleString() }}</div>
            <div class="stat-subtitle">
              已使用: {{ stats.usedCredits.toLocaleString() }} ({{ creditUsageRate }}%)
            </div>
          </div>
        </div>

        <!-- 兑换码统计 -->
        <div class="stat-card">
          <div class="stat-icon bg-red-500">
            <i class="fas fa-ticket-alt"></i>
          </div>
          <div class="stat-content">
            <h3 class="stat-title">兑换码</h3>
            <div class="stat-number">{{ stats.activeCodes }}</div>
            <div class="stat-subtitle">活跃兑换码</div>
          </div>
        </div>

        <!-- 系统公告 -->
        <div class="stat-card">
          <div class="stat-icon bg-indigo-500">
            <i class="fas fa-bullhorn"></i>
          </div>
          <div class="stat-content">
            <h3 class="stat-title">系统公告</h3>
            <div class="stat-number">{{ stats.announcements }}</div>
            <div class="stat-subtitle">已发布公告</div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="recent-activities">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-clock mr-2"></i>
            最近活动
          </h2>
          <button class="refresh-btn" @click="fetchDashboardData">
            <i class="fas fa-sync-alt"></i>
            刷新
          </button>
        </div>

        <div class="activity-list">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon">
              <i :class="getActivityIcon(activity.type)"></i>
            </div>
            <div class="activity-content">
              <div class="activity-description">{{ activity.description }}</div>
              <div class="activity-user">{{ activity.user }}</div>
            </div>
            <div class="activity-time">{{ activity.time }}</div>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-bolt mr-2"></i>
            快捷操作
          </h2>
        </div>

        <div class="action-grid">
          <router-link to="/admin/specified-emails" class="action-card">
            <i class="fas fa-envelope-open-text"></i>
            <span>管理指定邮箱</span>
          </router-link>

          <router-link to="/admin/user-temp-emails" class="action-card">
            <i class="fas fa-users"></i>
            <span>用户临时邮箱</span>
          </router-link>

          <router-link to="/admin/users" class="action-card">
            <i class="fas fa-user-friends"></i>
            <span>用户管理</span>
          </router-link>

          <router-link to="/admin/redemption-codes" class="action-card">
            <i class="fas fa-ticket-alt"></i>
            <span>兑换码管理</span>
          </router-link>

          <router-link to="/admin/announcements" class="action-card">
            <i class="fas fa-bullhorn"></i>
            <span>系统公告</span>
          </router-link>

          <router-link to="/admin/settings" class="action-card">
            <i class="fas fa-cog"></i>
            <span>系统设置</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard {
  min-height: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.loading-spinner {
  text-align: center;
}

.dashboard-content {
  space-y: 24px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 24px;
}

.stat-content {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.stat-subtitle {
  font-size: 12px;
  color: #9ca3af;
}

/* 最近活动 */
.recent-activities {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
}

.refresh-btn {
  padding: 8px 16px;
  background: #f3f4f6;
  border: none;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover {
  background: #e5e7eb;
}

.activity-list {
  space-y: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.activity-item:hover {
  background: #f9fafb;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.activity-user {
  font-size: 12px;
  color: #6b7280;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 快捷操作 */
.quick-actions {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  text-decoration: none;
  color: #6b7280;
  transition: all 0.2s ease;
}

.action-card:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
}

.action-card i {
  font-size: 24px;
  margin-bottom: 8px;
}

.action-card span {
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
