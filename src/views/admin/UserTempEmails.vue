<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()
const loading = ref(false)
const emails = ref([])
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('all')
const userFilter = ref('')

// 对话框状态
const showDetailDialog = ref(false)
const selectedEmail = ref(null)
const emailMessages = ref([])

// API 基础 URL
const apiUrl = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${adminStore.authToken}`
})

// 获取用户临时邮箱列表
const fetchUserTempEmails = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: pagination.value.limit.toString()
    })

    if (searchKeyword.value) {
      params.append('search', searchKeyword.value)
    }
    if (statusFilter.value !== 'all') {
      params.append('status', statusFilter.value)
    }
    if (userFilter.value) {
      params.append('user', userFilter.value)
    }

    const response = await fetch(`${apiUrl}/admin/user-temp-emails?${params}`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      emails.value = result.emails || []
      pagination.value = { ...pagination.value, ...result.pagination }
    } else {
      ElMessage.error(result.message || '获取邮箱列表失败')
    }
  } catch (error) {
    console.error('获取邮箱列表失败:', error)
    ElMessage.error('获取邮箱列表失败')
  } finally {
    loading.value = false
  }
}

// 查看邮箱详情
const handleViewDetail = async (email: any) => {
  selectedEmail.value = email
  showDetailDialog.value = true
  
  try {
    const response = await fetch(`${apiUrl}/admin/user-temp-emails/${email.id}/messages`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      emailMessages.value = result.messages || []
    } else {
      ElMessage.error(result.message || '获取邮件列表失败')
    }
  } catch (error) {
    console.error('获取邮件列表失败:', error)
    ElMessage.error('获取邮件列表失败')
  }
}

// 删除用户临时邮箱
const handleDeleteEmail = async (email: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${email.userEmail} 的邮箱 ${email.fullEmail} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`${apiUrl}/admin/user-temp-emails/${email.id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('邮箱删除成功')
      await fetchUserTempEmails()
    } else {
      ElMessage.error(result.message || '删除邮箱失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除邮箱失败:', error)
      ElMessage.error('删除邮箱失败')
    }
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'text-green-600'
    case 'expired': return 'text-red-600'
    case 'deleted': return 'text-gray-500'
    default: return 'text-gray-600'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'expired': return '已过期'
    case 'deleted': return '已删除'
    default: return '未知'
  }
}

// 计算属性
const filteredEmails = computed(() => {
  return emails.value.filter((email: any) => {
    const matchesSearch = !searchKeyword.value || 
      email.fullEmail.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      email.userEmail.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const matchesStatus = statusFilter.value === 'all' || email.status === statusFilter.value
    
    const matchesUser = !userFilter.value || 
      email.userEmail.toLowerCase().includes(userFilter.value.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesUser
  })
})

onMounted(() => {
  fetchUserTempEmails()
})
</script>

<template>
  <div class="user-temp-emails">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户临时邮箱管理</h1>
        <p class="page-subtitle">管理用户通过配额生成的临时邮箱</p>
      </div>
      
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-label">总邮箱数</span>
          <span class="stat-value">{{ pagination.total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">活跃邮箱</span>
          <span class="stat-value text-green-600">
            {{ filteredEmails.filter(e => e.status === 'active').length }}
          </span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索邮箱地址或用户邮箱..."
          @input="fetchUserTempEmails(1)"
        />
      </div>
      
      <div class="filter-group">
        <input
          v-model="userFilter"
          type="text"
          placeholder="筛选用户..."
          class="filter-input"
          @input="fetchUserTempEmails(1)"
        />
        
        <select v-model="statusFilter" @change="fetchUserTempEmails(1)" class="filter-select">
          <option value="all">全部状态</option>
          <option value="active">活跃</option>
          <option value="expired">已过期</option>
          <option value="deleted">已删除</option>
        </select>
      </div>
    </div>

    <!-- 邮箱列表 -->
    <div class="emails-section">
      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
        <p class="mt-2 text-gray-600">加载中...</p>
      </div>

      <div v-else-if="filteredEmails.length === 0" class="empty-state">
        <i class="fas fa-inbox text-4xl text-gray-400"></i>
        <p class="mt-4 text-gray-600">暂无用户临时邮箱数据</p>
      </div>

      <div v-else class="emails-table">
        <div class="table-header">
          <div class="col-email">邮箱地址</div>
          <div class="col-user">用户</div>
          <div class="col-status">状态</div>
          <div class="col-mails">邮件数</div>
          <div class="col-unread">未读数</div>
          <div class="col-date">创建时间</div>
          <div class="col-expire">过期时间</div>
          <div class="col-actions">操作</div>
        </div>

        <div class="table-body">
          <div 
            v-for="email in filteredEmails" 
            :key="email.id"
            class="table-row"
          >
            <div class="col-email">
              <div class="email-info">
                <div class="email-address">{{ email.fullEmail }}</div>
                <div class="email-domain">{{ email.domain }}</div>
              </div>
            </div>
            <div class="col-user">
              <div class="user-info">
                <div class="user-email">{{ email.userEmail }}</div>
                <div class="user-id">ID: {{ email.userId }}</div>
              </div>
            </div>
            <div class="col-status">
              <span :class="getStatusColor(email.status)">
                {{ getStatusText(email.status) }}
              </span>
            </div>
            <div class="col-mails">{{ email.mailCount }}</div>
            <div class="col-unread">
              <span v-if="email.unreadCount > 0" class="unread-badge">
                {{ email.unreadCount }}
              </span>
              <span v-else class="text-gray-400">0</span>
            </div>
            <div class="col-date">{{ formatDate(email.createdAt) }}</div>
            <div class="col-expire">{{ formatDate(email.expireAt) }}</div>
            <div class="col-actions">
              <button @click="handleViewDetail(email)" class="action-btn view-btn">
                <i class="fas fa-eye"></i>
              </button>
              <button @click="handleDeleteEmail(email)" class="action-btn delete-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          v-for="page in pagination.totalPages" 
          :key="page"
          @click="fetchUserTempEmails(page)"
          :class="[
            'page-btn',
            { 'page-btn-active': page === pagination.page }
          ]"
        >
          {{ page }}
        </button>
      </div>
    </div>

    <!-- 邮箱详情对话框 -->
    <div v-if="showDetailDialog" class="modal-overlay" @click="showDetailDialog = false">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <div>
            <h3>邮箱详情</h3>
            <p class="text-sm text-gray-600">{{ selectedEmail?.fullEmail }}</p>
          </div>
          <button @click="showDetailDialog = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <!-- 邮箱信息 -->
          <div class="email-detail-info">
            <div class="info-grid">
              <div class="info-item">
                <label>用户邮箱</label>
                <span>{{ selectedEmail?.userEmail }}</span>
              </div>
              <div class="info-item">
                <label>状态</label>
                <span :class="getStatusColor(selectedEmail?.status)">
                  {{ getStatusText(selectedEmail?.status) }}
                </span>
              </div>
              <div class="info-item">
                <label>邮件总数</label>
                <span>{{ selectedEmail?.mailCount }}</span>
              </div>
              <div class="info-item">
                <label>未读邮件</label>
                <span>{{ selectedEmail?.unreadCount }}</span>
              </div>
            </div>
          </div>

          <!-- 邮件列表 -->
          <div class="messages-section">
            <h4 class="messages-title">邮件列表</h4>
            
            <div v-if="emailMessages.length === 0" class="empty-messages">
              <i class="fas fa-envelope-open text-2xl text-gray-400"></i>
              <p class="mt-2 text-gray-600">暂无邮件</p>
            </div>
            
            <div v-else class="messages-list">
              <div 
                v-for="message in emailMessages" 
                :key="message.id"
                class="message-item"
              >
                <div class="message-header">
                  <div class="message-from">
                    <strong>{{ message.fromAddress }}</strong>
                    <span v-if="message.fromName" class="from-name">({{ message.fromName }})</span>
                  </div>
                  <div class="message-time">{{ formatDate(message.receivedAt) }}</div>
                </div>
                
                <div class="message-subject">{{ message.subject || '无主题' }}</div>
                
                <div v-if="message.verificationCode" class="verification-code">
                  <i class="fas fa-key"></i>
                  <span>验证码: <strong>{{ message.verificationCode }}</strong></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-temp-emails {
  max-width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

/* 搜索和筛选 */
.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.filter-group {
  display: flex;
  gap: 12px;
}

.filter-input,
.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

/* 邮箱列表 */
.emails-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.emails-table {
  width: 100%;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 0.8fr 0.8fr 0.8fr 1.2fr 1.2fr 1fr;
  gap: 16px;
  padding: 16px 24px;
  align-items: center;
}

.table-header {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background 0.2s ease;
}

.table-row:hover {
  background: #f9fafb;
}

.email-info,
.user-info {
  display: flex;
  flex-direction: column;
}

.email-address,
.user-email {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.email-domain,
.user-id {
  font-size: 12px;
  color: #6b7280;
}

.unread-badge {
  background: #ef4444;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 4px;
}

.view-btn {
  background: #dbeafe;
  color: #1e40af;
}

.view-btn:hover {
  background: #bfdbfe;
}

.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover {
  background: #fecaca;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 20px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background: #f3f4f6;
}

.page-btn-active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  margin: 20px;
}

.large-modal {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #374151;
}

.modal-body {
  padding: 24px;
}

/* 邮箱详情信息 */
.email-detail-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-item span {
  font-weight: 500;
  color: #1f2937;
}

/* 邮件列表 */
.messages-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
}

.messages-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.messages-list {
  max-height: 400px;
  overflow-y: auto;
}

.message-item {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.message-from {
  font-weight: 500;
  color: #1f2937;
}

.from-name {
  color: #6b7280;
  font-weight: normal;
}

.message-time {
  font-size: 12px;
  color: #9ca3af;
}

.message-subject {
  color: #374151;
  margin-bottom: 8px;
}

.verification-code {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  color: #92400e;
  font-size: 14px;
}

.verification-code strong {
  font-family: monospace;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header,
  .table-row {
    grid-template-columns: 2fr 1.5fr 0.8fr 0.8fr 1fr 1fr;
  }
  
  .col-unread,
  .col-expire {
    display: none;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-stats {
    justify-content: space-around;
  }
  
  .filters-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: stretch;
  }
  
  .filter-input,
  .filter-select {
    flex: 1;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    display: block;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
