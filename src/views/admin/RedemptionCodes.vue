<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()
const loading = ref(false)
const codes = ref([])
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('all')
const typeFilter = ref('all')

// 对话框状态
const showCreateDialog = ref(false)
const showBatchDialog = ref(false)

// 创建表单
const createForm = ref({
  code: '',
  credits: 100,
  description: '',
  expiresAt: '',
  maxUses: 1,
  type: 'manual'
})

// 批量生成表单
const batchForm = ref({
  count: 10,
  credits: 100,
  description: '',
  expiresAt: '',
  maxUses: 1,
  prefix: '',
  type: 'batch'
})

// API 基础 URL
const apiUrl = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${adminStore.authToken}`
})

// 获取兑换码列表
const fetchCodes = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: pagination.value.limit.toString()
    })

    if (searchKeyword.value) {
      params.append('search', searchKeyword.value)
    }
    if (statusFilter.value !== 'all') {
      params.append('status', statusFilter.value)
    }
    if (typeFilter.value !== 'all') {
      params.append('type', typeFilter.value)
    }

    const response = await fetch(`${apiUrl}/admin/redemption-codes?${params}`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      codes.value = result.codes || []
      pagination.value = { ...pagination.value, ...result.pagination }
    } else {
      ElMessage.error(result.message || '获取兑换码列表失败')
    }
  } catch (error) {
    console.error('获取兑换码列表失败:', error)
    ElMessage.error('获取兑换码列表失败')
  } finally {
    loading.value = false
  }
}

// 创建兑换码
const handleCreateCode = async () => {
  try {
    const response = await fetch(`${apiUrl}/admin/redemption-codes`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(createForm.value)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('兑换码创建成功')
      showCreateDialog.value = false
      resetCreateForm()
      await fetchCodes()
    } else {
      ElMessage.error(result.message || '创建兑换码失败')
    }
  } catch (error) {
    console.error('创建兑换码失败:', error)
    ElMessage.error('创建兑换码失败')
  }
}

// 批量生成兑换码
const handleBatchGenerate = async () => {
  try {
    const response = await fetch(`${apiUrl}/admin/redemption-codes/batch`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(batchForm.value)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(`成功生成 ${result.codes?.length || 0} 个兑换码`)
      showBatchDialog.value = false
      resetBatchForm()
      await fetchCodes()
    } else {
      ElMessage.error(result.message || '批量生成失败')
    }
  } catch (error) {
    console.error('批量生成失败:', error)
    ElMessage.error('批量生成失败')
  }
}

// 删除兑换码
const handleDeleteCode = async (code: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除兑换码 ${code.code} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`${apiUrl}/admin/redemption-codes/${code.id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('兑换码删除成功')
      await fetchCodes()
    } else {
      ElMessage.error(result.message || '删除兑换码失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除兑换码失败:', error)
      ElMessage.error('删除兑换码失败')
    }
  }
}

// 生成随机兑换码
const generateRandomCode = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  createForm.value.code = result
}

// 重置表单
const resetCreateForm = () => {
  createForm.value = {
    code: '',
    credits: 100,
    description: '',
    expiresAt: '',
    maxUses: 1,
    type: 'manual'
  }
}

const resetBatchForm = () => {
  batchForm.value = {
    count: 10,
    credits: 100,
    description: '',
    expiresAt: '',
    maxUses: 1,
    prefix: '',
    type: 'batch'
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取状态颜色
const getStatusColor = (code: any) => {
  if (code.usedCount >= code.maxUses) return 'text-gray-500'
  if (code.expiresAt && new Date(code.expiresAt) < new Date()) return 'text-red-600'
  return 'text-green-600'
}

// 获取状态文本
const getStatusText = (code: any) => {
  if (code.usedCount >= code.maxUses) return '已用完'
  if (code.expiresAt && new Date(code.expiresAt) < new Date()) return '已过期'
  return '可用'
}

// 计算属性
const filteredCodes = computed(() => {
  return codes.value.filter((code: any) => {
    const matchesSearch = !searchKeyword.value || 
      code.code.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      code.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const status = getStatusText(code)
    const matchesStatus = statusFilter.value === 'all' || 
      (statusFilter.value === 'available' && status === '可用') ||
      (statusFilter.value === 'used' && status === '已用完') ||
      (statusFilter.value === 'expired' && status === '已过期')
    
    const matchesType = typeFilter.value === 'all' || code.type === typeFilter.value
    
    return matchesSearch && matchesStatus && matchesType
  })
})

onMounted(() => {
  fetchCodes()
})
</script>

<template>
  <div class="redemption-codes">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">兑换码管理</h1>
        <p class="page-subtitle">管理系统兑换码，用于用户获取配额</p>
      </div>
      
      <div class="header-actions">
        <button @click="showCreateDialog = true" class="btn btn-primary">
          <i class="fas fa-plus mr-2"></i>
          创建兑换码
        </button>
        <button @click="showBatchDialog = true" class="btn btn-secondary">
          <i class="fas fa-layer-group mr-2"></i>
          批量生成
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索兑换码或描述..."
          @input="fetchCodes(1)"
        />
      </div>
      
      <div class="filter-group">
        <select v-model="statusFilter" @change="fetchCodes(1)" class="filter-select">
          <option value="all">全部状态</option>
          <option value="available">可用</option>
          <option value="used">已用完</option>
          <option value="expired">已过期</option>
        </select>
        
        <select v-model="typeFilter" @change="fetchCodes(1)" class="filter-select">
          <option value="all">全部类型</option>
          <option value="manual">手动创建</option>
          <option value="batch">批量生成</option>
        </select>
      </div>
    </div>

    <!-- 兑换码列表 -->
    <div class="codes-section">
      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i>
        <p class="mt-2 text-gray-600">加载中...</p>
      </div>

      <div v-else-if="filteredCodes.length === 0" class="empty-state">
        <i class="fas fa-ticket-alt text-4xl text-gray-400"></i>
        <p class="mt-4 text-gray-600">暂无兑换码数据</p>
      </div>

      <div v-else class="codes-table">
        <div class="table-header">
          <div class="col-code">兑换码</div>
          <div class="col-credits">配额</div>
          <div class="col-usage">使用情况</div>
          <div class="col-status">状态</div>
          <div class="col-type">类型</div>
          <div class="col-expires">过期时间</div>
          <div class="col-created">创建时间</div>
          <div class="col-actions">操作</div>
        </div>

        <div class="table-body">
          <div 
            v-for="code in filteredCodes" 
            :key="code.id"
            class="table-row"
          >
            <div class="col-code">
              <div class="code-info">
                <div class="code-value">{{ code.code }}</div>
                <div class="code-description">{{ code.description || '无描述' }}</div>
              </div>
            </div>
            <div class="col-credits">{{ code.credits }}</div>
            <div class="col-usage">
              <div class="usage-info">
                <div class="usage-count">{{ code.usedCount }} / {{ code.maxUses }}</div>
                <div class="usage-bar">
                  <div 
                    class="usage-progress" 
                    :style="{ width: `${(code.usedCount / code.maxUses) * 100}%` }"
                  ></div>
                </div>
              </div>
            </div>
            <div class="col-status">
              <span :class="getStatusColor(code)">
                {{ getStatusText(code) }}
              </span>
            </div>
            <div class="col-type">
              <span class="type-badge">{{ code.type === 'manual' ? '手动' : '批量' }}</span>
            </div>
            <div class="col-expires">
              {{ code.expiresAt ? formatDate(code.expiresAt) : '永不过期' }}
            </div>
            <div class="col-created">{{ formatDate(code.createdAt) }}</div>
            <div class="col-actions">
              <button @click="handleDeleteCode(code)" class="action-btn delete-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          v-for="page in pagination.totalPages" 
          :key="page"
          @click="fetchCodes(page)"
          :class="[
            'page-btn',
            { 'page-btn-active': page === pagination.page }
          ]"
        >
          {{ page }}
        </button>
      </div>
    </div>

    <!-- 创建兑换码对话框 -->
    <div v-if="showCreateDialog" class="modal-overlay" @click="showCreateDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>创建兑换码</h3>
          <button @click="showCreateDialog = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <form @submit.prevent="handleCreateCode" class="modal-body">
          <div class="form-group">
            <label>兑换码</label>
            <div class="code-input-group">
              <input 
                v-model="createForm.code" 
                type="text" 
                placeholder="输入兑换码或点击生成" 
                required 
              />
              <button type="button" @click="generateRandomCode" class="generate-btn">
                <i class="fas fa-dice"></i>
                生成
              </button>
            </div>
          </div>
          
          <div class="form-group">
            <label>配额数量</label>
            <input v-model.number="createForm.credits" type="number" min="1" required />
          </div>
          
          <div class="form-group">
            <label>描述</label>
            <input v-model="createForm.description" type="text" placeholder="兑换码用途描述" />
          </div>
          
          <div class="form-group">
            <label>最大使用次数</label>
            <input v-model.number="createForm.maxUses" type="number" min="1" required />
          </div>
          
          <div class="form-group">
            <label>过期时间（可选）</label>
            <input v-model="createForm.expiresAt" type="datetime-local" />
          </div>
          
          <div class="modal-actions">
            <button type="button" @click="showCreateDialog = false" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary">
              创建
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 批量生成对话框 -->
    <div v-if="showBatchDialog" class="modal-overlay" @click="showBatchDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>批量生成兑换码</h3>
          <button @click="showBatchDialog = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <form @submit.prevent="handleBatchGenerate" class="modal-body">
          <div class="form-group">
            <label>生成数量</label>
            <input v-model.number="batchForm.count" type="number" min="1" max="100" required />
          </div>
          
          <div class="form-group">
            <label>配额数量</label>
            <input v-model.number="batchForm.credits" type="number" min="1" required />
          </div>
          
          <div class="form-group">
            <label>前缀（可选）</label>
            <input v-model="batchForm.prefix" type="text" placeholder="兑换码前缀" />
          </div>
          
          <div class="form-group">
            <label>描述</label>
            <input v-model="batchForm.description" type="text" placeholder="批量兑换码用途描述" />
          </div>
          
          <div class="form-group">
            <label>最大使用次数</label>
            <input v-model.number="batchForm.maxUses" type="number" min="1" required />
          </div>
          
          <div class="form-group">
            <label>过期时间（可选）</label>
            <input v-model="batchForm.expiresAt" type="datetime-local" />
          </div>
          
          <div class="modal-actions">
            <button type="button" @click="showBatchDialog = false" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary">
              生成
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.redemption-codes {
  max-width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* 搜索和筛选 */
.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.filter-group {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

/* 兑换码列表 */
.codes-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.codes-table {
  width: 100%;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 2fr 0.8fr 1.2fr 0.8fr 0.8fr 1.2fr 1.2fr 0.8fr;
  gap: 16px;
  padding: 16px 24px;
  align-items: center;
}

.table-header {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background 0.2s ease;
}

.table-row:hover {
  background: #f9fafb;
}

.code-info {
  display: flex;
  flex-direction: column;
}

.code-value {
  font-weight: 500;
  color: #1f2937;
  font-family: monospace;
  margin-bottom: 2px;
}

.code-description {
  font-size: 12px;
  color: #6b7280;
}

.usage-info {
  display: flex;
  flex-direction: column;
}

.usage-count {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.usage-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.usage-progress {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.type-badge {
  padding: 4px 8px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover {
  background: #fecaca;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 20px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background: #f3f4f6;
}

.page-btn-active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  margin: 20px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.code-input-group {
  display: flex;
  gap: 8px;
}

.code-input-group input {
  flex: 1;
}

.generate-btn {
  padding: 10px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.generate-btn:hover {
  background: #e5e7eb;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header,
  .table-row {
    grid-template-columns: 2fr 0.8fr 1fr 0.8fr 1fr 1fr;
  }
  
  .col-type,
  .col-created {
    display: none;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filters-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: stretch;
  }
  
  .filter-select {
    flex: 1;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    display: block;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
  }
}
</style>
