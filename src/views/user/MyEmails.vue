<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '../../stores/user'
import { useUserTempEmailStore } from '@/stores/userTempEmail'

const userStore = useUserStore()
const tempEmailStore = useUserTempEmailStore()

const showCreateDialog = ref(false)
const showEmailDetail = ref(false)
const selectedEmail = ref<any>(null)
const createForm = ref({
  domain: 'ofun.my',
  customName: '',
  expireDays: 30
})

const availableDomains = ['ofun.my', 'ofun.io']

// 计算属性
const isLoading = computed(() => tempEmailStore.loading)
const emails = computed(() => tempEmailStore.emails)
const pagination = computed(() => tempEmailStore.pagination)

// 生命周期
onMounted(async () => {
  await tempEmailStore.loadEmails()
})

// 方法
const handleCreateEmail = async () => {
  try {
    await tempEmailStore.createEmail(createForm.value)
    showCreateDialog.value = false
    createForm.value.customName = ''
    // 重新加载邮箱列表
    await tempEmailStore.loadEmails()
  } catch (error) {
    console.error('创建邮箱失败:', error)
  }
}

const handleViewEmail = async (email: any) => {
  selectedEmail.value = email
  showEmailDetail.value = true
  // 加载邮件列表
  await tempEmailStore.loadEmailMessages(email.id)
}

const handleDeleteEmail = async (emailId: number) => {
  if (confirm('确定要删除这个邮箱吗？删除后无法恢复。')) {
    try {
      await tempEmailStore.deleteEmail(emailId)
      await tempEmailStore.loadEmails()
    } catch (error) {
      console.error('删除邮箱失败:', error)
    }
  }
}

const handleRefreshMessages = async () => {
  if (selectedEmail.value) {
    await tempEmailStore.loadEmailMessages(selectedEmail.value.id)
  }
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'text-green-600'
    case 'expired': return 'text-red-600'
    case 'deleted': return 'text-gray-500'
    default: return 'text-gray-600'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'expired': return '已过期'
    case 'deleted': return '已删除'
    default: return '未知'
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">我的邮箱</h1>
        <p class="mt-2 text-gray-600">管理您的临时邮箱，查看接收的验证码</p>
      </div>

      <!-- 操作栏 -->
      <div class="mb-6 flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">
            配额余额: <span class="font-semibold text-blue-600">{{ userStore.user?.credits || 0 }}</span>
          </span>
        </div>
        <button
          @click="showCreateDialog = true"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          创建新邮箱
        </button>
      </div>

      <!-- 邮箱列表 -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div v-if="isLoading" class="p-8 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-2 text-gray-600">加载中...</p>
        </div>

        <div v-else-if="emails.length === 0" class="p-8 text-center">
          <p class="text-gray-600">暂无邮箱，点击上方按钮创建您的第一个临时邮箱</p>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="email in emails"
            :key="email.id"
            class="p-6 hover:bg-gray-50 transition-colors"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <h3 class="text-lg font-medium text-gray-900">{{ email.fullEmail }}</h3>
                  <span :class="getStatusColor(email.status)" class="text-sm font-medium">
                    {{ getStatusText(email.status) }}
                  </span>
                  <span v-if="email.unreadCount > 0" class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {{ email.unreadCount }} 未读
                  </span>
                </div>
                <div class="mt-2 flex items-center space-x-6 text-sm text-gray-600">
                  <span>邮件数量: {{ email.mailCount }}</span>
                  <span>创建时间: {{ formatDate(email.createdAt) }}</span>
                  <span>过期时间: {{ formatDate(email.expireAt) }}</span>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <button
                  @click="handleViewEmail(email)"
                  class="text-blue-600 hover:text-blue-800 font-medium"
                >
                  查看邮件
                </button>
                <button
                  @click="handleDeleteEmail(email.id)"
                  class="text-red-600 hover:text-red-800 font-medium"
                  :disabled="email.status === 'deleted'"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination && pagination.totalPages > 1" class="mt-6 flex justify-center">
        <nav class="flex space-x-2">
          <button
            v-for="page in pagination.totalPages"
            :key="page"
            @click="tempEmailStore.loadEmails(page)"
            :class="[
              'px-3 py-2 rounded-md text-sm font-medium',
              page === pagination.page
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
            ]"
          >
            {{ page }}
          </button>
        </nav>
      </div>
    </div>

    <!-- 创建邮箱对话框 -->
    <div v-if="showCreateDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">创建新邮箱</h3>
        
        <form @submit.prevent="handleCreateEmail" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">域名</label>
            <select v-model="createForm.domain" class="w-full border border-gray-300 rounded-md px-3 py-2">
              <option v-for="domain in availableDomains" :key="domain" :value="domain">
                {{ domain }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              自定义名称 <span class="text-gray-500">(可选)</span>
            </label>
            <input
              v-model="createForm.customName"
              type="text"
              placeholder="留空则自动生成"
              class="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">有效期 (天)</label>
            <select v-model="createForm.expireDays" class="w-full border border-gray-300 rounded-md px-3 py-2">
              <option :value="7">7 天</option>
              <option :value="30">30 天</option>
              <option :value="90">90 天</option>
            </select>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="showCreateDialog = false"
              class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            >
              取消
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              :disabled="tempEmailStore.loading"
            >
              创建
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 邮件详情对话框 -->
    <div v-if="showEmailDetail" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg w-full max-w-4xl mx-4 h-5/6 flex flex-col">
        <div class="p-6 border-b border-gray-200 flex justify-between items-center">
          <div>
            <h3 class="text-lg font-medium text-gray-900">{{ selectedEmail?.fullEmail }}</h3>
            <p class="text-sm text-gray-600">邮件列表</p>
          </div>
          <div class="flex space-x-3">
            <button
              @click="handleRefreshMessages"
              class="px-3 py-1 text-blue-600 hover:text-blue-800 font-medium"
            >
              刷新
            </button>
            <button
              @click="showEmailDetail = false"
              class="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>
        
        <div class="flex-1 overflow-y-auto p-6">
          <div v-if="tempEmailStore.messages.length === 0" class="text-center py-8">
            <p class="text-gray-600">暂无邮件</p>
          </div>
          
          <div v-else class="space-y-4">
            <div
              v-for="message in tempEmailStore.messages"
              :key="message.id"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="flex justify-between items-start mb-2">
                <div>
                  <h4 class="font-medium text-gray-900">{{ message.subject || '无主题' }}</h4>
                  <p class="text-sm text-gray-600">来自: {{ message.fromAddress }}</p>
                </div>
                <span class="text-xs text-gray-500">{{ formatDate(message.receivedAt) }}</span>
              </div>
              
              <div v-if="message.verificationCode" class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm font-medium text-yellow-800">验证码</p>
                <p class="text-lg font-mono font-bold text-yellow-900">{{ message.verificationCode }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
