import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from './user'

// 用户临时邮箱接口定义
export interface UserTempEmail {
  id: number
  emailName: string
  domain: string
  fullEmail: string
  status: 'active' | 'expired' | 'deleted'
  isActive: boolean
  mailCount: number
  unreadCount: number
  createdAt: string
  updatedAt: string
  expireAt: string
}

export interface UserTempEmailMessage {
  id: number
  messageId: string
  fromAddress: string
  fromName?: string
  subject?: string
  verificationCode?: string
  isRead: boolean
  receivedAt: string
}

export interface CreateEmailRequest {
  domain: string
  customName?: string
  expireDays?: number
}

export interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

export const useUserTempEmailStore = defineStore('userTempEmail', () => {
  const userStore = useUserStore()
  
  // 状态
  const emails = ref<UserTempEmail[]>([])
  const messages = ref<UserTempEmailMessage[]>([])
  const pagination = ref<Pagination | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeEmails = computed(() => 
    emails.value.filter(email => email.status === 'active')
  )

  const totalUnreadCount = computed(() =>
    emails.value.reduce((total, email) => total + email.unreadCount, 0)
  )

  // API 基础 URL
  const API_BASE = import.meta.env.VITE_API_URL || 'http://localhost:8787'

  // 获取认证头
  const getAuthHeaders = () => {
    const token = userStore.token
    if (!token) {
      throw new Error('未登录')
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  }

  // 加载用户邮箱列表
  const loadEmails = async (page = 1, limit = 20, status = 'active') => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(
        `${API_BASE}/api/user/temp-emails?page=${page}&limit=${limit}&status=${status}`,
        {
          method: 'GET',
          headers: getAuthHeaders()
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message || '获取邮箱列表失败')
      }

      emails.value = result.data.emails
      pagination.value = result.data.pagination

    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取邮箱列表失败'
      console.error('Load emails error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 创建新邮箱
  const createEmail = async (request: CreateEmailRequest) => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/api/user/temp-emails`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message || '创建邮箱失败')
      }

      // 更新用户配额
      if (result.creditsRemaining !== undefined) {
        userStore.updateCredits(result.creditsRemaining)
      }

      return result.data

    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建邮箱失败'
      console.error('Create email error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取邮箱详情
  const getEmailDetail = async (emailId: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/api/user/temp-emails/${emailId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message || '获取邮箱详情失败')
      }

      return result.data

    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取邮箱详情失败'
      console.error('Get email detail error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 加载邮箱的邮件列表
  const loadEmailMessages = async (emailId: number, page = 1, limit = 20) => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(
        `${API_BASE}/api/user/temp-emails/${emailId}/messages?page=${page}&limit=${limit}`,
        {
          method: 'GET',
          headers: getAuthHeaders()
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message || '获取邮件列表失败')
      }

      messages.value = result.data.messages

      // 更新邮箱的未读数量
      const emailIndex = emails.value.findIndex(email => email.id === emailId)
      if (emailIndex !== -1) {
        emails.value[emailIndex].unreadCount = 0
      }

      return result.data

    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取邮件列表失败'
      console.error('Load email messages error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除邮箱
  const deleteEmail = async (emailId: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/api/user/temp-emails/${emailId}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message || '删除邮箱失败')
      }

      // 从本地列表中移除
      const emailIndex = emails.value.findIndex(email => email.id === emailId)
      if (emailIndex !== -1) {
        emails.value.splice(emailIndex, 1)
      }

    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除邮箱失败'
      console.error('Delete email error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 清空状态
  const clearState = () => {
    emails.value = []
    messages.value = []
    pagination.value = null
    error.value = null
  }

  return {
    // 状态
    emails,
    messages,
    pagination,
    loading,
    error,

    // 计算属性
    activeEmails,
    totalUnreadCount,

    // 方法
    loadEmails,
    createEmail,
    getEmailDetail,
    loadEmailMessages,
    deleteEmail,
    clearState
  }
})
