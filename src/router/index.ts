import { createRouter, createWebHashHistory } from 'vue-router'
import TempMailBox from '../views/tempMail/index.vue'
import { useUserStore } from '../stores/user'
import { useAdminStore } from '../stores/admin'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/home/<USER>'),
    meta: {
      title: '临时邮箱 - 首页'
    }
  },
  {
    path: '/apikey_mail',
    name: 'ApiKeyMail',
    component: TempMailBox,
    meta: {
      title: '授权密钥查询'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/Login.vue'),
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/auth/Register.vue'),
    meta: {
      title: '用户注册'
    }
  },
  {
    path: '/test-auth',
    name: 'TestAuth',
    component: () => import('../views/TestAuth.vue'),
    meta: {
      title: '邮件验证测试'
    }
  },
  {
    path: '/auth/github/callback',
    name: 'GitHubCallback',
    component: () => import('../views/auth/GitHubCallback.vue'),
    meta: {
      title: 'GitHub 登录处理中...'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/user/Profile.vue'),
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  },
  {
    path: '/my-emails',
    name: 'MyEmails',
    component: () => import('../views/user/MyEmails.vue'),
    meta: {
      title: '我的邮箱',
      requiresAuth: true
    }
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: () => import('../views/pricing/index.vue'),
    meta: {
      title: '配额购买'
    }
  },
  {
    path: '/user',
    redirect: '/profile'
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: () => import('../views/admin/Login.vue'),
    meta: {
      title: '管理员登录'
    }
  },
  {
    path: '/admin',
    component: () => import('../views/admin/Layout.vue'),
    meta: {
      title: '后台管理',
      requiresAdmin: true
    },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('../views/admin/Dashboard.vue'),
        meta: {
          title: '管理后台 - 仪表盘'
        }
      },
      {
        path: 'specified-emails',
        name: 'AdminSpecifiedEmails',
        component: () => import('../views/admin/SpecifiedEmails.vue'),
        meta: {
          title: '管理后台 - 指定邮箱管理'
        }
      },
      {
        path: 'user-temp-emails',
        name: 'AdminUserTempEmails',
        component: () => import('../views/admin/UserTempEmails.vue'),
        meta: {
          title: '管理后台 - 用户临时邮箱管理'
        }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('../views/admin/Users.vue'),
        meta: {
          title: '管理后台 - 用户管理'
        }
      },
      {
        path: 'redemption-codes',
        name: 'AdminRedemptionCodes',
        component: () => import('../views/admin/RedemptionCodes.vue'),
        meta: {
          title: '管理后台 - 兑换码管理'
        }
      },
      {
        path: 'announcements',
        name: 'AdminAnnouncements',
        component: () => import('../views/admin/Announcements.vue'),
        meta: {
          title: '管理后台 - 系统公告'
        }
      },
      {
        path: 'settings',
        name: 'AdminSettings',
        component: () => import('../views/admin/Settings.vue'),
        meta: {
          title: '管理后台 - 系统设置'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫 - 设置页面标题和认证检查
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - TempMail.ofun.my`
  }

  // 获取用户store和管理员store
  const userStore = useUserStore()
  const adminStore = useAdminStore()

  // 检查是否需要用户认证
  if (to.meta?.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，重定向到登录页面
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 检查是否需要管理员权限
  if (to.meta?.requiresAdmin) {
    if (!adminStore.isLoggedIn) {
      // 管理员未登录，重定向到管理员登录页面
      next('/admin/login')
      return
    }
  }

  // 如果已登录用户访问登录或注册页面，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register') && userStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

export default router
