#!/bin/bash

# 测试用户临时邮箱功能
API_BASE="http://localhost:8787/api"

echo "🧪 测试用户临时邮箱功能..."

# 首先需要登录获取 token
echo "📝 请先登录获取 token..."
echo "可以使用以下命令登录:"
echo "curl -X POST $API_BASE/auth/login -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"your-password\"}'"
echo ""
echo "请输入您的 JWT token:"
read -r TOKEN

if [ -z "$TOKEN" ]; then
    echo "❌ Token 不能为空"
    exit 1
fi

echo "🔑 使用 token: ${TOKEN:0:20}..."

# 测试获取用户临时邮箱列表
echo ""
echo "📋 测试获取用户临时邮箱列表..."
curl -X GET "$API_BASE/user/temp-emails" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" | jq '.'

# 测试创建用户临时邮箱
echo ""
echo "📧 测试创建用户临时邮箱..."
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/user/temp-emails" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "domain": "ofun.my",
        "customName": "test-user-email",
        "expireDays": 30
    }')

echo "$CREATE_RESPONSE" | jq '.'

# 提取邮箱 ID
EMAIL_ID=$(echo "$CREATE_RESPONSE" | jq -r '.data.id // empty')

if [ -n "$EMAIL_ID" ] && [ "$EMAIL_ID" != "null" ]; then
    echo ""
    echo "✅ 邮箱创建成功，ID: $EMAIL_ID"
    
    # 测试获取邮箱详情
    echo ""
    echo "📄 测试获取邮箱详情..."
    curl -X GET "$API_BASE/user/temp-emails/$EMAIL_ID" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" | jq '.'
    
    # 测试获取邮箱邮件列表
    echo ""
    echo "📬 测试获取邮箱邮件列表..."
    curl -X GET "$API_BASE/user/temp-emails/$EMAIL_ID/messages" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" | jq '.'
    
    # 询问是否删除测试邮箱
    echo ""
    echo "🗑️  是否删除测试邮箱? (y/N)"
    read -r DELETE_CONFIRM
    
    if [ "$DELETE_CONFIRM" = "y" ] || [ "$DELETE_CONFIRM" = "Y" ]; then
        echo "删除邮箱..."
        curl -X DELETE "$API_BASE/user/temp-emails/$EMAIL_ID" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" | jq '.'
        echo "✅ 测试邮箱已删除"
    fi
else
    echo "❌ 邮箱创建失败"
fi

echo ""
echo "🎉 测试完成！"
