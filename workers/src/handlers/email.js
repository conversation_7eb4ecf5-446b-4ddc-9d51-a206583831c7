// 邮件处理器
import PostalMime from 'postal-mime';

export async function handleEmailProcessing(message, env) {
  try {
    console.log('Processing email from:', message.from, 'to:', message.to);

    // 使用 postal-mime 解析邮件
    const parsedEmail = await parseEmailWithPostalMime(message.raw);

    // 提取收件人邮箱地址
    const toEmail = message.to;
    console.log('Recipient email:', toEmail);

    // 提取邮件内容
    const subject = parsedEmail.subject || '无主题';
    const textContent = parsedEmail.text || '';
    const htmlContent = parsedEmail.html || '';

    // 提取发件人信息
    const fromAddress = parsedEmail.from?.address || message.from;
    const fromName = parsedEmail.from?.name || '';

    // 尝试提取验证码
    const verificationCode = extractVerificationCode(textContent + ' ' + htmlContent);

    // 首先查找用户临时邮箱记录
    const userTempEmail = await env.DB.prepare(
      'SELECT * FROM user_temp_emails WHERE full_email = ? AND status = "active"'
    ).bind(toEmail).first();

    if (userTempEmail) {
      // 处理用户临时邮箱
      await handleUserTempEmailMessage(env, userTempEmail, {
        fromAddress,
        fromName,
        toEmail,
        subject,
        textContent,
        htmlContent,
        verificationCode
      });
      console.log('User temp email processed successfully for:', toEmail);
      return;
    }

    // 查找 ApiKeyMail 邮箱记录（现有系统）
    const apiKeyEmail = await env.DB.prepare(
      'SELECT * FROM emails WHERE (mail || "@" || domain) = ?'
    ).bind(toEmail).first();

    if (apiKeyEmail) {
      // 处理 ApiKeyMail 邮箱（保持现有逻辑）
      await handleApiKeyEmailMessage(env, apiKeyEmail, {
        fromAddress,
        fromName,
        toEmail,
        subject,
        textContent,
        htmlContent,
        verificationCode
      });
      console.log('ApiKey email processed successfully for:', toEmail);
      return;
    }

    console.log('No email record found for:', toEmail);

  } catch (error) {
    console.error('Email processing error:', error);
  }
}

// 处理用户临时邮箱的邮件
async function handleUserTempEmailMessage(env, tempEmail, emailData) {
  // 生成唯一的消息ID
  const messageId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  await env.DB.prepare(`
    INSERT INTO user_temp_email_messages (
      temp_email_id,
      message_id,
      from_address,
      from_name,
      to_address,
      subject,
      text_content,
      html_content,
      verification_code,
      received_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
  `).bind(
    tempEmail.id,
    messageId,
    emailData.fromAddress,
    emailData.fromName,
    emailData.toEmail,
    emailData.subject,
    emailData.textContent,
    emailData.htmlContent,
    emailData.verificationCode
  ).run();
}

// 处理 ApiKeyMail 邮箱的邮件（保持现有逻辑）
async function handleApiKeyEmailMessage(env, emailRecord, emailData) {
  // 保存到 mails 表（现有系统）
  await env.DB.prepare(`
    INSERT INTO mails (
      email_id,
      from_address,
      from_name,
      subject,
      text_content,
      html_content,
      verification_code,
      received_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
  `).bind(
    emailRecord.id,
    emailData.fromAddress,
    emailData.fromName,
    emailData.subject,
    emailData.textContent,
    emailData.htmlContent,
    emailData.verificationCode
  ).run();

  // 同时保存到 mail_messages 表（统一格式）
  const messageId = `api_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  await env.DB.prepare(`
    INSERT INTO mail_messages (
      email_id,
      message_id,
      from_address,
      to_address,
      subject,
      text_content,
      html_content,
      received_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
  `).bind(
    emailRecord.id,
    messageId,
    emailData.fromAddress,
    emailData.toEmail,
    emailData.subject,
    emailData.textContent,
    emailData.htmlContent
  ).run();
}

// 使用 postal-mime 解析邮件
async function parseEmailWithPostalMime(rawEmail) {
  try {
    const parser = new PostalMime();
    const email = await parser.parse(rawEmail);
    return email;
  } catch (error) {
    console.error('Email parsing error:', error);
    return {
      subject: '解析失败',
      text: '邮件解析失败',
      html: '',
      from: { address: '', name: '' }
    };
  }
}

// 提取验证码的函数
function extractVerificationCode(content) {
  // 常见的验证码模式
  const patterns = [
    /验证码[：:\s]*([0-9]{4,8})/i,
    /verification code[：:\s]*([0-9]{4,8})/i,
    /code[：:\s]*([0-9]{4,8})/i,
    /pin[：:\s]*([0-9]{4,8})/i,
    /\b([0-9]{4,8})\b.*验证/i,
    /\b([0-9]{4,8})\b.*code/i,
    /您的验证码是[：:\s]*([0-9]{4,8})/i,
    /your verification code is[：:\s]*([0-9]{4,8})/i,
    /\b([0-9]{6})\b/g // 6位数字（最常见的验证码格式）
  ];

  for (const pattern of patterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}
