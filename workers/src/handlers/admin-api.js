// 管理员相关 API 处理器

// 验证管理员权限
async function verifyAdminAuth(request, env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7);
  // 这里应该验证 JWT token，暂时简化处理
  return token === 'admin-token-123';
}

export async function handleAdminAPI(request, env, corsHeaders, path) {
  // 检查管理员权限
  if (path !== '/admin/login' && !await verifyAdminAuth(request, env)) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 路由处理
  if (path === '/admin/login') {
    return handleAdminLogin(request, env, corsHeaders);
  }

  if (path === '/admin/emails' || path.startsWith('/admin/emails/')) {
    return handleAdminEmails(request, env, corsHeaders, path);
  }

  if (path === '/admin/user-temp-emails' || path.startsWith('/admin/user-temp-emails/')) {
    return handleAdminUserTempEmails(request, env, corsHeaders, path);
  }

  if (path === '/admin/users' || path.startsWith('/admin/users/')) {
    return handleAdminUsers(request, env, corsHeaders, path);
  }

  if (path === '/admin/redemption-codes' || path.startsWith('/admin/redemption-codes/')) {
    return handleAdminRedemptionCodes(request, env, corsHeaders, path);
  }

  if (path === '/admin/announcements' || path.startsWith('/admin/announcements/')) {
    return handleAdminAnnouncements(request, env, corsHeaders, path);
  }

  if (path === '/admin/settings') {
    return handleAdminSettings(request, env, corsHeaders);
  }

  return new Response(JSON.stringify({
    success: false,
    message: 'Admin API endpoint not found'
  }), {
    status: 404,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 管理员登录
async function handleAdminLogin(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { username, password } = await request.json();

  // 查询管理员
  const admin = await env.DB.prepare(
    'SELECT * FROM admins WHERE username = ?'
  ).bind(username).first();

  if (!admin) {
    return new Response(JSON.stringify({
      success: false,
      message: '用户名或密码错误'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 简单密码验证（实际项目中应该使用 bcrypt）
  if (password !== 'admin123') {
    return new Response(JSON.stringify({
      success: false,
      message: '用户名或密码错误'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 生成简单的 token（实际项目中应该使用 JWT）
  const token = 'admin-token-123';

  return new Response(JSON.stringify({
    success: true,
    token: token,
    message: '登录成功'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取邮箱列表（管理员）
async function handleAdminEmails(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 获取邮箱列表
    const emails = await env.DB.prepare(`
      SELECT e.*, u.email as user_email, u.username,
             (SELECT COUNT(*) FROM mail_messages WHERE email_id = e.id) as mail_count
      FROM emails e
      LEFT JOIN users u ON e.user_id = u.id
      ORDER BY e.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all();

    // 获取总数
    const totalResult = await env.DB.prepare('SELECT COUNT(*) as total FROM emails').first();
    const total = totalResult.total;

    return new Response(JSON.stringify({
      success: true,
      emails: emails.results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Admin get emails error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 管理员用户管理
async function handleAdminUsers(request, env, corsHeaders) {
  // 验证管理员权限
  const authResult = await verifyAdminAuth(request);
  if (!authResult.success) {
    return new Response(JSON.stringify(authResult), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  if (request.method === 'GET') {
    return await getUsers(request, env, corsHeaders);
  } else if (request.method === 'PUT') {
    return await updateUserCredits(request, env, corsHeaders);
  } else {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }
}

async function getUsers(request, env, corsHeaders) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100);
    const search = url.searchParams.get('search');
    const status = url.searchParams.get('status');
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    let params = [];

    if (search) {
      whereClause += ' AND (u.email LIKE ? OR u.username LIKE ? OR u.display_name LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }

    if (status && ['active', 'suspended', 'deleted'].includes(status)) {
      whereClause += ' AND u.status = ?';
      params.push(status);
    }

    // 获取用户列表
    const users = await env.DB.prepare(`
      SELECT u.id, u.email, u.username, u.display_name, u.avatar_url,
             u.email_verified, u.status, u.created_at, u.last_login_at,
             uc.balance, uc.total_earned, uc.total_spent,
             (SELECT COUNT(*) FROM emails WHERE user_id = u.id) as email_count
      FROM users u
      LEFT JOIN user_credits uc ON u.id = uc.user_id
      ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(...params, limit, offset).all();

    // 获取总数
    const countResult = await env.DB.prepare(`
      SELECT COUNT(*) as total FROM users u ${whereClause}
    `).bind(...params).first();

    return new Response(JSON.stringify({
      success: true,
      users: users.results.map(user => ({
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        emailVerified: Boolean(user.email_verified),
        status: user.status,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at,
        credits: {
          balance: user.balance || 0,
          totalEarned: user.total_earned || 0,
          totalSpent: user.total_spent || 0
        },
        emailCount: user.email_count
      })),
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / limit)
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Admin get users error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取用户列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function updateUserCredits(request, env, corsHeaders) {
  try {
    const { userId, amount, description } = await request.json();

    if (!userId || amount === undefined || !description) {
      return new Response(JSON.stringify({
        success: false,
        message: '参数不完整'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取用户当前配额
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(userId).first();

    const currentBalance = credits ? credits.balance : 0;
    const newBalance = currentBalance + amount;

    if (newBalance < 0) {
      return new Response(JSON.stringify({
        success: false,
        message: '配额不能为负数'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 更新用户配额
    if (credits) {
      await env.DB.prepare(
        'UPDATE user_credits SET balance = ?, total_earned = total_earned + ?, updated_at = datetime("now") WHERE user_id = ?'
      ).bind(newBalance, Math.max(0, amount), userId).run();
    } else {
      await env.DB.prepare(
        'INSERT INTO user_credits (user_id, balance, total_earned, total_spent, created_at, updated_at) VALUES (?, ?, ?, 0, datetime("now"), datetime("now"))'
      ).bind(userId, newBalance, Math.max(0, amount)).run();
    }

    // 记录交易
    await env.DB.prepare(`
      INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, reference_type, created_at)
      VALUES (?, 'admin_adjust', ?, ?, ?, 'admin', datetime('now'))
    `).bind(userId, amount, newBalance, description).run();

    return new Response(JSON.stringify({
      success: true,
      message: '配额调整成功',
      newBalance: newBalance
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Update user credits error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '配额调整失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleAdminRedemptionCodes(request, env, corsHeaders) {
  // 验证管理员权限
  const authResult = await verifyAdminAuth(request);
  if (!authResult.success) {
    return new Response(JSON.stringify(authResult), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  if (request.method === 'GET') {
    return await getRedemptionCodes(request, env, corsHeaders);
  } else if (request.method === 'POST') {
    return await createRedemptionCodes(request, env, corsHeaders);
  } else if (request.method === 'PUT') {
    return await updateRedemptionCode(request, env, corsHeaders);
  } else {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }
}

async function getRedemptionCodes(request, env, corsHeaders) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100);
    const status = url.searchParams.get('status'); // active, expired, used_up
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    let params = [];

    if (status === 'active') {
      whereClause += ' AND rc.is_active = 1 AND (rc.expires_at IS NULL OR rc.expires_at > datetime("now")) AND (rc.max_uses = 0 OR rc.used_count < rc.max_uses)';
    } else if (status === 'expired') {
      whereClause += ' AND rc.expires_at IS NOT NULL AND rc.expires_at <= datetime("now")';
    } else if (status === 'used_up') {
      whereClause += ' AND rc.max_uses > 0 AND rc.used_count >= rc.max_uses';
    }

    // 获取兑换码列表
    const codes = await env.DB.prepare(`
      SELECT rc.*,
             (SELECT COUNT(*) FROM redemption_code_uses WHERE code_id = rc.id) as actual_used_count
      FROM redemption_codes rc
      ${whereClause}
      ORDER BY rc.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(...params, limit, offset).all();

    // 获取总数
    const countResult = await env.DB.prepare(`
      SELECT COUNT(*) as total FROM redemption_codes rc ${whereClause}
    `).bind(...params).first();

    return new Response(JSON.stringify({
      success: true,
      codes: codes.results.map(code => ({
        id: code.id,
        code: code.code,
        credits: code.credits,
        description: code.description,
        maxUses: code.max_uses,
        usedCount: code.actual_used_count,
        expiresAt: code.expires_at,
        isActive: Boolean(code.is_active),
        createdAt: code.created_at,
        updatedAt: code.updated_at
      })),
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / limit)
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get redemption codes error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取兑换码列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function createRedemptionCodes(request, env, corsHeaders) {
  try {
    const { count, credits, description, maxUses, expiresAt, prefix } = await request.json();

    if (!count || !credits || count < 1 || count > 100) {
      return new Response(JSON.stringify({
        success: false,
        message: '参数错误，数量必须在1-100之间'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const codes = [];

    for (let i = 0; i < count; i++) {
      const code = generateRedemptionCode(prefix);

      // 插入兑换码
      const result = await env.DB.prepare(`
        INSERT INTO redemption_codes (code, credits, description, max_uses, expires_at, is_active, created_by, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 1, 1, datetime('now'), datetime('now'))
      `).bind(
        code,
        credits,
        description || '',
        maxUses || 1,
        expiresAt || null
      ).run();

      codes.push({
        id: result.meta.last_row_id,
        code: code,
        credits: credits,
        description: description || '',
        maxUses: maxUses || 1,
        expiresAt: expiresAt || null
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: `成功生成 ${count} 个兑换码`,
      codes: codes
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Create redemption codes error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '生成兑换码失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleAdminAnnouncements(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员公告管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 管理用户临时邮箱
async function handleAdminUserTempEmails(request, env, corsHeaders, path) {
  if (path === '/admin/user-temp-emails' && request.method === 'GET') {
    return handleGetUserTempEmails(request, env, corsHeaders);
  }

  if (path.startsWith('/admin/user-temp-emails/') && path.endsWith('/messages') && request.method === 'GET') {
    const emailId = path.split('/')[3];
    return handleGetUserTempEmailMessages(request, env, corsHeaders, emailId);
  }

  if (path.startsWith('/admin/user-temp-emails/') && request.method === 'DELETE') {
    const emailId = path.split('/')[3];
    return handleDeleteUserTempEmail(request, env, corsHeaders, emailId);
  }

  return new Response(JSON.stringify({
    success: false,
    message: '用户临时邮箱管理功能暂未完全实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取用户临时邮箱列表
async function handleGetUserTempEmails(request, env, corsHeaders) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;
    const search = url.searchParams.get('search') || '';
    const status = url.searchParams.get('status') || '';
    const user = url.searchParams.get('user') || '';

    let whereClause = '1=1';
    const params = [];

    if (search) {
      whereClause += ' AND (ute.full_email LIKE ? OR u.email LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    if (status && status !== 'all') {
      whereClause += ' AND ute.status = ?';
      params.push(status);
    }

    if (user) {
      whereClause += ' AND u.email LIKE ?';
      params.push(`%${user}%`);
    }

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_temp_emails ute
      JOIN users u ON ute.user_id = u.id
      WHERE ${whereClause}
    `;
    const countResult = await env.DB.prepare(countQuery).bind(...params).first();

    // 获取邮箱列表
    const emailsQuery = `
      SELECT
        ute.id,
        ute.user_id,
        ute.email_name,
        ute.domain,
        ute.full_email,
        ute.status,
        ute.created_at,
        ute.updated_at,
        ute.expire_at,
        u.email as user_email,
        (SELECT COUNT(*) FROM user_temp_email_messages WHERE temp_email_id = ute.id) as mail_count,
        (SELECT COUNT(*) FROM user_temp_email_messages WHERE temp_email_id = ute.id AND is_read = FALSE) as unread_count
      FROM user_temp_emails ute
      JOIN users u ON ute.user_id = u.id
      WHERE ${whereClause}
      ORDER BY ute.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const emailsResult = await env.DB.prepare(emailsQuery).bind(...params, limit, offset).all();

    return new Response(JSON.stringify({
      success: true,
      emails: emailsResult.results.map(email => ({
        id: email.id,
        userId: email.user_id,
        emailName: email.email_name,
        domain: email.domain,
        fullEmail: email.full_email,
        status: email.status,
        userEmail: email.user_email,
        mailCount: email.mail_count,
        unreadCount: email.unread_count,
        createdAt: email.created_at,
        updatedAt: email.updated_at,
        expireAt: email.expire_at
      })),
      pagination: {
        page,
        limit,
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / limit)
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('获取用户临时邮箱列表失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 获取用户临时邮箱的邮件列表
async function handleGetUserTempEmailMessages(request, env, corsHeaders, emailId) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 验证邮箱存在
    const email = await env.DB.prepare(
      'SELECT id FROM user_temp_emails WHERE id = ?'
    ).bind(emailId).first();

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱不存在'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取邮件列表
    const messagesResult = await env.DB.prepare(`
      SELECT id, message_id, from_address, from_name, subject, verification_code, is_read, received_at
      FROM user_temp_email_messages
      WHERE temp_email_id = ?
      ORDER BY received_at DESC
      LIMIT ? OFFSET ?
    `).bind(emailId, limit, offset).all();

    return new Response(JSON.stringify({
      success: true,
      messages: messagesResult.results.map(msg => ({
        id: msg.id,
        messageId: msg.message_id,
        fromAddress: msg.from_address,
        fromName: msg.from_name,
        subject: msg.subject,
        verificationCode: msg.verification_code,
        isRead: Boolean(msg.is_read),
        receivedAt: msg.received_at
      }))
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('获取邮件列表失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮件列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 删除用户临时邮箱
async function handleDeleteUserTempEmail(request, env, corsHeaders, emailId) {
  try {
    // 验证邮箱存在
    const email = await env.DB.prepare(
      'SELECT id FROM user_temp_emails WHERE id = ?'
    ).bind(emailId).first();

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱不存在'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 软删除邮箱
    await env.DB.prepare(
      'UPDATE user_temp_emails SET status = "deleted", updated_at = datetime("now") WHERE id = ?'
    ).bind(emailId).run();

    return new Response(JSON.stringify({
      success: true,
      message: '邮箱删除成功'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('删除邮箱失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '删除邮箱失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 管理系统设置
async function handleAdminSettings(request, env, corsHeaders) {
  if (request.method === 'GET') {
    // 返回默认设置（实际项目中应该从数据库读取）
    return new Response(JSON.stringify({
      success: true,
      settings: {
        emailSettings: {
          defaultDomains: ['ofun.my', 'ofun.io'],
          maxEmailsPerUser: 10,
          defaultEmailExpireDays: 30,
          enableAutoCleanup: true,
          cleanupDays: 7
        },
        userSettings: {
          enableRegistration: true,
          defaultCredits: 100,
          maxCreditsPerUser: 10000,
          enableEmailVerification: false
        },
        redemptionSettings: {
          enableRedemption: true,
          maxCodesPerBatch: 100,
          defaultCodeLength: 12
        },
        systemSettings: {
          siteName: 'TempMail',
          siteDescription: '临时邮箱服务',
          enableMaintenance: false,
          maintenanceMessage: '系统维护中，请稍后再试',
          enableAnalytics: true
        }
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  if (request.method === 'PUT') {
    // 保存设置（实际项目中应该保存到数据库）
    return new Response(JSON.stringify({
      success: true,
      message: '设置保存成功'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  return new Response(JSON.stringify({
    success: false,
    message: '不支持的请求方法'
  }), {
    status: 405,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 辅助函数：生成兑换码
function generateRedemptionCode(prefix = '') {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  const length = 12 - prefix.length;

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return result;
}
