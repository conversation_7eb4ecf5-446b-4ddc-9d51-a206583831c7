// 用户临时邮箱 API 处理器
import { getUserFromRequest } from '../utils/auth.js';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// 生成随机邮箱名称
function generateRandomEmailName() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 处理用户临时邮箱相关请求
export async function handleUserTempEmailAPI(request, env, path) {
  // 处理 CORS 预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // 路由处理
  if (path === '/api/user/temp-emails' && request.method === 'GET') {
    return handleGetUserTempEmails(request, env);
  }
  
  if (path === '/api/user/temp-emails' && request.method === 'POST') {
    return handleCreateUserTempEmail(request, env);
  }
  
  if (path.startsWith('/api/user/temp-emails/') && request.method === 'GET') {
    return handleGetUserTempEmailDetail(request, env, path);
  }
  
  if (path.startsWith('/api/user/temp-emails/') && path.endsWith('/messages') && request.method === 'GET') {
    return handleGetUserTempEmailMessages(request, env, path);
  }
  
  if (path.startsWith('/api/user/temp-emails/') && request.method === 'DELETE') {
    return handleDeleteUserTempEmail(request, env, path);
  }

  return new Response('Not Found', { status: 404, headers: corsHeaders });
}

// 获取用户的临时邮箱列表
async function handleGetUserTempEmails(request, env) {
  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;
    const status = url.searchParams.get('status') || 'active';

    // 获取总数
    const countResult = await env.DB.prepare(
      'SELECT COUNT(*) as total FROM user_temp_emails WHERE user_id = ? AND status = ?'
    ).bind(user.id, status).first();

    // 获取邮箱列表
    const emails = await env.DB.prepare(`
      SELECT id, email_name, domain, full_email, status, created_at, updated_at, expire_at,
             (expire_at > datetime("now")) as is_active
      FROM user_temp_emails 
      WHERE user_id = ? AND status = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind(user.id, status, limit, offset).all();

    // 获取每个邮箱的邮件数量
    const emailsWithCounts = await Promise.all(
      emails.results.map(async (email) => {
        const mailCount = await env.DB.prepare(
          'SELECT COUNT(*) as count FROM user_temp_email_messages WHERE temp_email_id = ?'
        ).bind(email.id).first();

        const unreadCount = await env.DB.prepare(
          'SELECT COUNT(*) as count FROM user_temp_email_messages WHERE temp_email_id = ? AND is_read = FALSE'
        ).bind(email.id).first();

        return {
          id: email.id,
          emailName: email.email_name,
          domain: email.domain,
          fullEmail: email.full_email,
          status: email.status,
          isActive: Boolean(email.is_active),
          mailCount: mailCount.count,
          unreadCount: unreadCount.count,
          createdAt: email.created_at,
          updatedAt: email.updated_at,
          expireAt: email.expire_at
        };
      })
    );

    return new Response(JSON.stringify({
      success: true,
      data: {
        emails: emailsWithCounts,
        pagination: {
          page,
          limit,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / limit)
        }
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get user temp emails error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 创建用户临时邮箱
async function handleCreateUserTempEmail(request, env) {
  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查用户配额
    const credits = await env.DB.prepare(
      'SELECT balance FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    if (!credits || credits.balance < 1) {
      return new Response(JSON.stringify({
        success: false,
        message: '配额不足，无法生成临时邮箱'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { domain, customName, expireDays = 30 } = await request.json();
    
    if (!domain) {
      return new Response(JSON.stringify({
        success: false,
        message: '请选择域名'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 生成邮箱名称
    const emailName = customName || generateRandomEmailName();
    const fullEmail = `${emailName}@${domain}`;

    // 检查邮箱是否已存在
    const existingEmail = await env.DB.prepare(
      'SELECT id FROM user_temp_emails WHERE full_email = ? AND status = "active"'
    ).bind(fullEmail).first();

    if (existingEmail) {
      return new Response(JSON.stringify({
        success: false,
        message: '该邮箱已存在'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 保存到数据库
    const insertResult = await env.DB.prepare(`
      INSERT INTO user_temp_emails (user_id, email_name, domain, full_email, expire_at)
      VALUES (?, ?, ?, ?, datetime('now', '+${expireDays} days'))
    `).bind(user.id, emailName, domain, fullEmail).run();

    // 扣除配额
    await env.DB.prepare(
      'UPDATE user_credits SET balance = balance - 1, total_spent = total_spent + 1, updated_at = datetime("now") WHERE user_id = ?'
    ).bind(user.id).run();

    // 记录交易
    await env.DB.prepare(`
      INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, reference_type, reference_id)
      VALUES (?, 'spend', 1, ?, '生成用户临时邮箱', 'temp_email', ?)
    `).bind(user.id, credits.balance - 1, insertResult.meta.last_row_id).run();

    return new Response(JSON.stringify({
      success: true,
      data: {
        id: insertResult.meta.last_row_id,
        fullEmail: fullEmail,
        emailName: emailName,
        domain: domain,
        expiresAt: new Date(Date.now() + expireDays * 24 * 60 * 60 * 1000).toISOString()
      },
      creditsRemaining: credits.balance - 1
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Create user temp email error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '创建临时邮箱失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 获取用户临时邮箱详情
async function handleGetUserTempEmailDetail(request, env, path) {
  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const emailId = path.split('/').pop();
    if (!emailId || isNaN(parseInt(emailId))) {
      return new Response(JSON.stringify({
        success: false,
        message: '无效的邮箱ID'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取邮箱详情
    const email = await env.DB.prepare(`
      SELECT id, email_name, domain, full_email, status, created_at, updated_at, expire_at,
             (expire_at > datetime("now")) as is_active
      FROM user_temp_emails 
      WHERE id = ? AND user_id = ?
    `).bind(emailId, user.id).first();

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱不存在或无权访问'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取邮件统计
    const mailStats = await env.DB.prepare(
      'SELECT COUNT(*) as total_mails, COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread_mails FROM user_temp_email_messages WHERE temp_email_id = ?'
    ).bind(emailId).first();

    return new Response(JSON.stringify({
      success: true,
      data: {
        id: email.id,
        emailName: email.email_name,
        domain: email.domain,
        fullEmail: email.full_email,
        status: email.status,
        isActive: Boolean(email.is_active),
        totalMails: mailStats.total_mails,
        unreadMails: mailStats.unread_mails,
        createdAt: email.created_at,
        updatedAt: email.updated_at,
        expireAt: email.expire_at
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get user temp email detail error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱详情失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 获取用户临时邮箱的邮件列表
async function handleGetUserTempEmailMessages(request, env, path) {
  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const emailId = path.split('/')[4]; // /api/user/temp-emails/{id}/messages
    if (!emailId || isNaN(parseInt(emailId))) {
      return new Response(JSON.stringify({
        success: false,
        message: '无效的邮箱ID'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证邮箱所有权
    const email = await env.DB.prepare(
      'SELECT id FROM user_temp_emails WHERE id = ? AND user_id = ?'
    ).bind(emailId, user.id).first();

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱不存在或无权访问'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 获取邮件总数
    const countResult = await env.DB.prepare(
      'SELECT COUNT(*) as total FROM user_temp_email_messages WHERE temp_email_id = ?'
    ).bind(emailId).first();

    // 获取邮件列表
    const messages = await env.DB.prepare(`
      SELECT id, message_id, from_address, from_name, subject, verification_code, is_read, received_at
      FROM user_temp_email_messages
      WHERE temp_email_id = ?
      ORDER BY received_at DESC
      LIMIT ? OFFSET ?
    `).bind(emailId, limit, offset).all();

    // 标记邮件为已读
    if (messages.results.length > 0) {
      const unreadMessageIds = messages.results
        .filter(msg => !msg.is_read)
        .map(msg => msg.id);

      if (unreadMessageIds.length > 0) {
        const placeholders = unreadMessageIds.map(() => '?').join(',');
        await env.DB.prepare(
          `UPDATE user_temp_email_messages SET is_read = TRUE WHERE id IN (${placeholders})`
        ).bind(...unreadMessageIds).run();
      }
    }

    return new Response(JSON.stringify({
      success: true,
      data: {
        messages: messages.results.map(msg => ({
          id: msg.id,
          messageId: msg.message_id,
          fromAddress: msg.from_address,
          fromName: msg.from_name,
          subject: msg.subject,
          verificationCode: msg.verification_code,
          isRead: Boolean(msg.is_read),
          receivedAt: msg.received_at
        })),
        pagination: {
          page,
          limit,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / limit)
        }
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get user temp email messages error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮件列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 删除用户临时邮箱
async function handleDeleteUserTempEmail(request, env, path) {
  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const emailId = path.split('/').pop();
    if (!emailId || isNaN(parseInt(emailId))) {
      return new Response(JSON.stringify({
        success: false,
        message: '无效的邮箱ID'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证邮箱所有权
    const email = await env.DB.prepare(
      'SELECT id FROM user_temp_emails WHERE id = ? AND user_id = ?'
    ).bind(emailId, user.id).first();

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱不存在或无权访问'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 软删除邮箱（标记为已删除）
    await env.DB.prepare(
      'UPDATE user_temp_emails SET status = "deleted", updated_at = datetime("now") WHERE id = ?'
    ).bind(emailId).run();

    return new Response(JSON.stringify({
      success: true,
      message: '邮箱删除成功'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Delete user temp email error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '删除邮箱失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}
