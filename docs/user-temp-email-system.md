# 用户临时邮箱系统

## 概述

根据您的需求，我已经为系统添加了全新的用户临时邮箱功能，与现有的 ApiKeyMail 系统完全分离。用户可以通过配额生成临时邮箱，在个人中心查看和管理邮箱，无需 secret 验证即可直接接收验证码。

## 主要特性

### 1. 独立的数据库表结构
- **user_temp_emails**: 用户临时邮箱表
- **user_temp_email_messages**: 用户临时邮箱邮件表
- 与现有的 `emails` 和 `mail_messages` 表完全分离

### 2. 用户配额系统集成
- 创建邮箱消耗 1 个配额
- 自动记录配额交易
- 实时更新用户余额

### 3. 无需 Secret 验证
- 用户登录后可直接查看自己的邮箱
- 基于 JWT token 的身份验证
- 安全的用户权限控制

### 4. 完整的邮件接收功能
- 自动提取验证码
- 支持 HTML 和文本邮件
- 邮件已读状态管理

## 数据库结构

### user_temp_emails 表
```sql
CREATE TABLE user_temp_emails (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    email_name TEXT NOT NULL,
    domain TEXT NOT NULL,
    full_email TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expire_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### user_temp_email_messages 表
```sql
CREATE TABLE user_temp_email_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    temp_email_id INTEGER NOT NULL,
    message_id TEXT UNIQUE NOT NULL,
    from_address TEXT NOT NULL,
    from_name TEXT,
    to_address TEXT NOT NULL,
    subject TEXT,
    text_content TEXT,
    html_content TEXT,
    verification_code TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    received_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (temp_email_id) REFERENCES user_temp_emails (id)
);
```

## API 接口

### 1. 获取用户邮箱列表
```
GET /api/user/temp-emails
Authorization: Bearer {token}
```

### 2. 创建临时邮箱
```
POST /api/user/temp-emails
Authorization: Bearer {token}
Content-Type: application/json

{
  "domain": "ofun.my",
  "customName": "my-email",  // 可选
  "expireDays": 30
}
```

### 3. 获取邮箱详情
```
GET /api/user/temp-emails/{id}
Authorization: Bearer {token}
```

### 4. 获取邮箱邮件列表
```
GET /api/user/temp-emails/{id}/messages
Authorization: Bearer {token}
```

### 5. 删除邮箱
```
DELETE /api/user/temp-emails/{id}
Authorization: Bearer {token}
```

## 前端功能

### 1. 我的邮箱页面 (`/my-emails`)
- 邮箱列表展示
- 创建新邮箱对话框
- 邮件查看功能
- 验证码高亮显示

### 2. 导航集成
- 用户下拉菜单中添加"我的邮箱"选项
- 配额余额显示
- 未读邮件数量提示

### 3. Pinia Store 管理
- `useUserTempEmailStore`: 管理用户临时邮箱状态
- 自动更新配额余额
- 邮件已读状态管理

## 邮件处理逻辑

### 统一邮件处理器
修改了 `workers/src/handlers/email.js`，现在支持两套邮箱系统：

1. **用户临时邮箱**: 保存到 `user_temp_email_messages` 表
2. **ApiKeyMail 邮箱**: 保存到 `mails` 和 `mail_messages` 表

### 自动验证码提取
支持多种验证码格式：
- 中文: "验证码：123456"
- 英文: "verification code: 123456"
- 6位数字模式匹配

## 部署说明

### 1. 数据库迁移
```bash
wrangler d1 execute ofun-email-db-local --local --file="migrations/0008_add_user_temp_emails.sql"
```

### 2. 启动开发环境
```bash
npm run dev:full
```

### 3. 测试 API
```bash
./scripts/test-user-temp-emails.sh
```

## 安全特性

1. **用户权限隔离**: 用户只能访问自己的邮箱
2. **JWT 身份验证**: 所有 API 都需要有效的 token
3. **软删除**: 邮箱删除采用状态标记，不直接删除数据
4. **配额控制**: 防止用户无限制创建邮箱

## 与现有系统的关系

- **完全独立**: 新系统与 ApiKeyMail 系统数据完全分离
- **共享邮件处理**: 使用相同的邮件接收和解析逻辑
- **统一用户系统**: 基于现有的用户认证和配额系统

## 后续扩展建议

1. **邮箱管理功能**
   - 邮箱重命名
   - 邮箱延期
   - 邮箱分组

2. **邮件功能增强**
   - 邮件搜索
   - 邮件导出
   - 邮件转发

3. **统计功能**
   - 邮箱使用统计
   - 验证码统计
   - 用户行为分析

## 测试建议

1. 登录用户账户
2. 访问 `/my-emails` 页面
3. 创建新的临时邮箱
4. 向邮箱发送测试邮件
5. 查看邮件和验证码提取效果
