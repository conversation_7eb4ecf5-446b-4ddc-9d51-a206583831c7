-- 用户临时邮箱系统数据库迁移
-- 创建用户临时邮箱表（与现有的 emails 表分离）
CREATE TABLE IF NOT EXISTS user_temp_emails (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    email_name TEXT NOT NULL,
    domain TEXT NOT NULL,
    full_email TEXT NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'expired', 'deleted')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expire_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 创建用户临时邮箱邮件表
CREATE TABLE IF NOT EXISTS user_temp_email_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    temp_email_id INTEGER NOT NULL,
    message_id TEXT UNIQUE NOT NULL,
    from_address TEXT NOT NULL,
    from_name TEXT,
    to_address TEXT NOT NULL,
    subject TEXT,
    text_content TEXT,
    html_content TEXT,
    verification_code TEXT, -- 自动提取的验证码
    is_read BOOLEAN DEFAULT FALSE,
    received_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (temp_email_id) REFERENCES user_temp_emails (id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_temp_emails_user_id ON user_temp_emails (user_id);
CREATE INDEX IF NOT EXISTS idx_user_temp_emails_full_email ON user_temp_emails (full_email);
CREATE INDEX IF NOT EXISTS idx_user_temp_emails_status ON user_temp_emails (status);
CREATE INDEX IF NOT EXISTS idx_user_temp_emails_expire_at ON user_temp_emails (expire_at);
CREATE INDEX IF NOT EXISTS idx_user_temp_emails_created_at ON user_temp_emails (created_at);

CREATE INDEX IF NOT EXISTS idx_user_temp_email_messages_temp_email_id ON user_temp_email_messages (temp_email_id);
CREATE INDEX IF NOT EXISTS idx_user_temp_email_messages_message_id ON user_temp_email_messages (message_id);
CREATE INDEX IF NOT EXISTS idx_user_temp_email_messages_received_at ON user_temp_email_messages (received_at);
CREATE INDEX IF NOT EXISTS idx_user_temp_email_messages_verification_code ON user_temp_email_messages (verification_code);
CREATE INDEX IF NOT EXISTS idx_user_temp_email_messages_is_read ON user_temp_email_messages (is_read);

-- 创建触发器：更新用户临时邮箱时自动更新 updated_at
CREATE TRIGGER IF NOT EXISTS update_user_temp_emails_timestamp
AFTER UPDATE ON user_temp_emails
BEGIN
    UPDATE user_temp_emails SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建触发器：自动标记过期的邮箱
CREATE TRIGGER IF NOT EXISTS auto_expire_user_temp_emails
AFTER INSERT ON user_temp_email_messages
BEGIN
    UPDATE user_temp_emails 
    SET status = 'expired', updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.temp_email_id 
    AND expire_at <= datetime('now') 
    AND status = 'active';
END;

-- 修复现有的 email.js 中的表名不一致问题
-- 创建 mails 表（如果不存在），用于兼容现有的 email.js 处理逻辑
CREATE TABLE IF NOT EXISTS mails (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email_id INTEGER NOT NULL,
    from_address TEXT NOT NULL,
    from_name TEXT,
    subject TEXT,
    text_content TEXT,
    html_content TEXT,
    verification_code TEXT,
    received_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
);

-- 为 mails 表创建索引
CREATE INDEX IF NOT EXISTS idx_mails_email_id ON mails (email_id);
CREATE INDEX IF NOT EXISTS idx_mails_received_at ON mails (received_at);
CREATE INDEX IF NOT EXISTS idx_mails_verification_code ON mails (verification_code);
