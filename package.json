{"name": "temp-mail-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:worker": "wrangler dev --env local --port 8787", "dev:setup": "./scripts/dev-setup.sh", "dev:start": "./scripts/dev.sh", "dev:full": "./scripts/start-dev.sh", "dev:test-email": "./scripts/test-email.sh", "dev:test-flow": "./scripts/test-complete-flow.sh", "build": "vue-tsc -b && vite build", "build:worker": "wrangler deploy", "deploy:prod": "./scripts/deploy-production.sh", "preview": "vite preview", "db:migrate": "wrangler d1 migrations apply ofun-email-db-local --local", "db:migrate:prod": "wrangler d1 migrations apply ofun-email-db"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify-json/carbon": "^1.2.11", "@iconify-json/fa": "^1.2.1", "@iconify-json/fa-solid": "^1.2.1", "@iconify-json/mdi": "^1.2.3", "@types/dompurify": "^3.0.5", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@unocss/vite": "^66.3.3", "@vueuse/core": "^13.6.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "element-plus": "^2.10.4", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "postal-mime": "^2.4.4", "unocss": "^66.3.3", "vercel": "^44.6.5", "vue": "^3.5.17", "vue-router": "^4.5.1", "wrangler": "^4.26.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}